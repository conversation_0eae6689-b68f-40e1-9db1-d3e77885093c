{"$schema": "./node_modules/@angular/cli/lib/config/schema.json", "version": 1, "newProjectRoot": "projects", "projects": {"front-end": {"projectType": "application", "schematics": {"@schematics/angular:component": {"style": "scss"}}, "root": "", "sourceRoot": "src", "prefix": "app", "architect": {"build": {"builder": "@angular-devkit/build-angular:browser", "options": {"outputPath": "dist/front-end", "index": "src/index.html", "main": "src/main.ts", "polyfills": ["zone.js"], "tsConfig": "tsconfig.app.json", "inlineStyleLanguage": "scss", "assets": [{"glob": "**/*", "input": "public"}, "src/assets", {"glob": "**/*", "input": "./node_modules/@ant-design/icons-angular/src/inline-svg/", "output": "/assets/"}], "styles": ["src/styles/styles.scss", "src/styles/fonts.scss"], "scripts": ["node_modules/apexcharts/dist/apexcharts.min.js"], "allowedCommonJsDependencies": ["@messageformat/core", "apexcharts"]}, "configurations": {"production": {"budgets": [{"type": "initial", "maximumWarning": "2MB", "maximumError": "2MB"}, {"type": "anyComponentStyle", "maximumWarning": "2kB", "maximumError": "4kB"}], "outputHashing": "all"}, "development": {"optimization": false, "extractLicenses": false, "sourceMap": true}}, "defaultConfiguration": "production"}, "serve": {"builder": "@angular-devkit/build-angular:dev-server", "configurations": {"production": {"buildTarget": "front-end:build:production"}, "development": {"buildTarget": "front-end:build:development"}}, "defaultConfiguration": "development", "options": {"proxyConfig": "proxy/proxy.conf.js", "host": "dashboard.lkq.dev", "allowedHosts": ["dashboard.lkq.dev"], "sslKey": "./proxy/dashboard.lkq.dev-key.pem", "sslCert": "./proxy/dashboard.lkq.dev.pem", "ssl": true}}, "extract-i18n": {"builder": "@angular-devkit/build-angular:extract-i18n"}}}}, "cli": {"analytics": false}}