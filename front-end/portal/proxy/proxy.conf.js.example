const winston = require('winston');

function logProvider() {
  return winston.createLogger({
    level: 'debug',
    format: winston.format.combine(
      winston.format.splat(),
      winston.format.simple()
    ),
    transports: [new winston.transports.Console()],
  });
}

var PROXY_CONF = {
  "/api": {
    target: "http://fource-back-end-api.test/",
    "pathRewrite": {
      "^/api": ""
    },
    secure: false,
    logLevel: "info", // debug | info | warning | error
    logProvider,
    changeOrigin: true
  }
};

module.exports = PROXY_CONF;
