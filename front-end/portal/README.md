# Project description

## Installation

1. use `npm install` to install all dependencies.
2. Copy the file [proxy/proxy.conf.js.example](proxy/proxy.conf.js.example) to [proxy/proxy.conf.js](proxy/proxy.conf.js) and adjust to correct back-end url.
3. Add the `127.0.0.1 dashboard.fource.dev` to your hosts file.
4. Trust the root certificate [proxy/rootCA.pem](proxy/rootCA.pem) on your system or browser.
5. Copy the file [public/config.example.json](public/config.example.json) to [public/config.json](public/config.json) and adjust the values to your configuration.

## Regenerating SSL
The front-end project uses HTTPS connection. The SSL certificates are generated using `mkcert`, as long as they are not expired, you don't need to regenerate them.

If the certificates are expired, you can regenerate them by following the steps below:
- Navigate to the [proxy](proxy) folder
- `CAROOT=$PWD mkcert dashboard.fource.dev`
