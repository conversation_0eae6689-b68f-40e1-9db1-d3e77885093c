{
  "compileOnSave": false,
  "compilerOptions": {
    "baseUrl": "./",
    "outDir": "./dist/out-tsc",
    "strict": true,
    "noImplicitOverride": true,
    "noPropertyAccessFromIndexSignature": true,
    "noImplicitReturns": true,
    "noFallthroughCasesInSwitch": true,
    "skipLibCheck": true,
    "esModuleInterop": true,
    "sourceMap": true,
    "declaration": false,
    "experimentalDecorators": true,
    "moduleResolution": "bundler",
    "importHelpers": true,
    "target": "ES2022",
    "module": "ES2022",
    "noUnusedLocals": true,
    "noUnusedParameters": true,
    "lib": [
      "ES2022",
      "dom"
    ],
    "paths": {
      "@api/*": ["src/api/*"],
      "@guards/*": ["src/app/guards/*"],
      "@interceptors/*": ["src/app/interceptors/*"],
      "@interfaces/*": ["src/app/interfaces/*"],
      "@layout/*": ["src/app/layout/*"],
      "@requests/*": ["src/app/requests/*"],
      "@responses/*": ["src/app/responses/*"],
      "@services/*": ["src/app/services/*"],
      "@components/*": ["src/app/components/*"],
    }
  },
  "angularCompilerOptions": {
    "enableI18nLegacyMessageIdFormat": false,
    "strictInjectionParameters": true,
    "strictInputAccessModifiers": true,
    "strictTemplates": true
  }
}
