@font-face {
  font-family: 'SharpGrotesk';
  src: url('/fonts/sharp_grotesk/SharpGrotesk-Black-20.ttf') format('truetype'),
  url('/fonts/sharp_grotesk/SharpGrotesk-Black-20.otf') format('opentype');
  font-weight: 900; /* Black */
  font-style: normal;
}

@font-face {
  font-family: 'SharpGrotesk';
  src: url('/fonts/sharp_grotesk/SharpGrotesk-Black-italic-20.ttf') format('truetype'),
  url('/fonts/sharp_grotesk/SharpGrotesk-Black-italic-20.otf') format('opentype');
  font-weight: 900; /* Black */
  font-style: italic;
  font-display: swap;
}

@font-face {
  font-family: 'SharpGrotesk';
  src: url('/fonts/sharp_grotesk/SharpGrotesk-Bold-20.ttf') format('truetype'),
  url('/fonts/sharp_grotesk/SharpGrotesk-Bold-20.otf') format('opentype');
  font-weight: 700; /* Bold */
  font-style: normal;
}

@font-face {
  font-family: 'SharpGrotesk';
  src: url('/fonts/sharp_grotesk/SharpGrotesk-Bold-italic-20.ttf') format('truetype'),
  url('/fonts/sharp_grotesk/SharpGrotesk-Bold-italic-20.otf') format('opentype');
  font-weight: 700; /* Bold */
  font-style: italic;
  font-display: swap;
}

@font-face {
  font-family: 'SharpGrotesk';
  src: url('/fonts/sharp_grotesk/SharpGrotesk-SmBold-20.ttf') format('truetype'),
  url('/fonts/sharp_grotesk/SharpGrotesk-SmBold-20.otf') format('opentype');
  font-weight: 600; /* Semi bold */
  font-style: normal;
}

@font-face {
  font-family: 'SharpGrotesk';
  src: url('/fonts/sharp_grotesk/SharpGrotesk-SmBold-italic-20.ttf') format('truetype'),
  url('/fonts/sharp_grotesk/SharpGrotesk-SmBold-italic-20.otf') format('opentype');
  font-weight: 600; /* Semi bold */
  font-style: italic;
  font-display: swap;
}

@font-face {
  font-family: 'SharpGrotesk';
  src: url('/fonts/sharp_grotesk/SharpGrotesk-Medium-20.ttf') format('truetype'),
  url('/fonts/sharp_grotesk/SharpGrotesk-Medium-20.otf') format('opentype');
  font-weight: 500; /* Medium */
  font-style: normal;
}

@font-face {
  font-family: 'SharpGrotesk';
  src: url('/fonts/sharp_grotesk/SharpGrotesk-Medium-italic-20.ttf') format('truetype'),
  url('/fonts/sharp_grotesk/SharpGrotesk-Medium-italic-20.otf') format('opentype');
  font-weight: 500; /* Medium */
  font-style: italic;
  font-display: swap;
}

@font-face {
  font-family: 'SharpGrotesk';
  src: url('/fonts/sharp_grotesk/SharpGrotesk-Book-20.ttf') format('truetype'),
  url('/fonts/sharp_grotesk/SharpGrotesk-Book-20.otf') format('opentype');
  font-weight: 400; /* Normal */
  font-style: normal;
}

@font-face {
  font-family: 'SharpGrotesk';
  src: url('/fonts/sharp_grotesk/SharpGrotesk-Book-italic-20.ttf') format('truetype'),
  url('/fonts/sharp_grotesk/SharpGrotesk-Book-italic-20.otf') format('opentype');
  font-weight: 400; /* Normal */
  font-style: italic;
  font-display: swap;
}

@font-face {
  font-family: 'SharpGrotesk';
  src: url('/fonts/sharp_grotesk/SharpGrotesk-Light-20.ttf') format('truetype'),
  url('/fonts/sharp_grotesk/SharpGrotesk-Light-20.otf') format('opentype');
  font-weight: 300; /* Light */
  font-style: normal;
}

@font-face {
  font-family: 'SharpGrotesk';
  src: url('/fonts/sharp_grotesk/SharpGrotesk-Light-italic-20.ttf') format('truetype'),
  url('/fonts/sharp_grotesk/SharpGrotesk-Light-italic-20.otf') format('opentype');
  font-weight: 300; /* Light */
  font-style: italic;
  font-display: swap;
}

@font-face {
  font-family: 'SharpGrotesk';
  src: url('/fonts/sharp_grotesk/SharpGrotesk-Thin-20.ttf') format('truetype'),
  url('/fonts/sharp_grotesk/SharpGrotesk-Thin-20.otf') format('opentype');
  font-weight: 100; /* Thin */
  font-style: normal;
}

@font-face {
  font-family: 'SharpGrotesk';
  src: url('/fonts/sharp_grotesk/SharpGrotesk-Thin-italic-20.ttf') format('truetype'),
  url('/fonts/sharp_grotesk/SharpGrotesk-Thin-italic-20.otf') format('opentype');
  font-weight: 100; /* Thin */
  font-style: italic;
  font-display: swap;
}

