// NG-Zorro
@import "node_modules/ng-zorro-antd/ng-zorro-antd.min.css";

// Tailwind
@import 'tailwindcss/base';
@import 'tailwindcss/components';
@import 'tailwindcss/utilities';

// NG-Zorro
nz-select .ant-select-selection-search-input {
  display: none !important;
  pointer-events: none !important;;
  caret-color: transparent !important;;
}

// Form
input.ng-invalid.ng-touched, select.ng-invalid.ng-touched, textarea.ng-invalid.ng-touched {
  @apply text-red-800 border-red-700 border;
}

.ant-select-selector {
  max-height: 2rem;
}
