import { WidgetAccuracy } from '@api/enums/dashboard/widget-accuracy.enum';

export interface WidgetEventAnalyticsDataPoint {
  date: string;
  value: number;
  valueFormatted: string;
  name: string;
  comparison?: number;
  comparisonFormatted: string | null;
}

export interface WidgetEventAnalytics {
  values: WidgetEventAnalyticsDataPoint[];
  total: number;
  totalFormatted: string;
  comparison: number;
  comparisonFormatted: string;
  accuracy: WidgetAccuracy;
}
