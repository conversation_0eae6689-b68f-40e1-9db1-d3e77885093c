import { Section } from './section.interface';
import { WidgetType } from '@api/enums/dashboard/widget-type.enum';
import { WidgetDataType } from '@api/enums/dashboard/widget-data-type.enum';

export interface Widget {
  id: number;
  title: string;
  data_type: WidgetDataType;
  type: WidgetType;
  section: Section | null;
  target: number | null;
  color: string;
  inverse_comparison_indicator: boolean;
}
