import { inject, Injectable } from '@angular/core';
import { HttpClient } from '@angular/common/http';
import { Observable } from 'rxjs';
import { ConfigService } from '@services/config.service';

@Injectable({
  providedIn: 'root',
})
export class HelpdeskService {
  // Injects
  private httpClient = inject(HttpClient);
  private configService = inject(ConfigService);

  // State
  private readonly endpoint: string | undefined;

  constructor() {
    this.endpoint = this.configService.config?.environment.endpoint;
  }

  public store(subject: string, message: string): Observable<null> {
    return this.httpClient.post<null>(
      `${this.endpoint}/api/settings/helpdesk/request`,
      {
        subject,
        message,
      },
    );
  }
}
