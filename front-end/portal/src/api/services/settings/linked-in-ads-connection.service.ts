import { inject, Injectable } from '@angular/core';
import { HttpClient, HttpParams } from '@angular/common/http';
import { Observable } from 'rxjs';
import { ConfigService } from '@services/config.service';
import { DataResponse } from '@responses/data.response';
import { MetadataResponse } from '@responses/metadata.response';
import { PaginatedResponse } from '@responses/paginated.response';
import { LinkedInAdsConnection } from '@api/interfaces/settings/linked-in-ads-connection.interface';
import { LinkedInAdsConnectionMetadata } from '@api/interfaces/settings/linked-in-ads-connection-metadata.interface';

@Injectable({
  providedIn: 'root',
})
export class LinkedInAdsConnectionService {
  // Injects
  private httpClient = inject(HttpClient);
  private configService = inject(ConfigService);

  // State
  private readonly endpoint: string | undefined;

  constructor() {
    this.endpoint = this.configService.config?.environment.endpoint;
  }

  public index(
    page?: number,
  ): Observable<PaginatedResponse<LinkedInAdsConnection>> {
    let parameters = new HttpParams();

    if (page) {
      parameters = parameters.set('page', page);
    }

    return this.httpClient.get<PaginatedResponse<LinkedInAdsConnection>>(
      `${this.endpoint}/api/settings/connections/linked_in/ads`,
      {
        params: parameters,
      },
    );
  }

  public show(
    linkedInAccountId: number,
  ): Observable<
    DataResponse<LinkedInAdsConnection> &
      MetadataResponse<LinkedInAdsConnectionMetadata>
  > {
    return this.httpClient.get<
      DataResponse<LinkedInAdsConnection> &
        MetadataResponse<LinkedInAdsConnectionMetadata>
    >(
      `${this.endpoint}/api/settings/connections/linked_in/ads/${linkedInAccountId}`,
    );
  }

  public destroy(linkedInAccountId: number): Observable<void> {
    return this.httpClient.delete<void>(
      `${this.endpoint}/api/settings/connections/linked_in/ads/${linkedInAccountId}`,
    );
  }
}
