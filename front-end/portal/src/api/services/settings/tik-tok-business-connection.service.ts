import { inject, Injectable } from '@angular/core';
import { HttpClient, HttpParams } from '@angular/common/http';
import { Observable } from 'rxjs';
import { ConfigService } from '@services/config.service';
import { PaginatedResponse } from '@responses/paginated.response';
import { TikTokBusinessConnection } from '@api/interfaces/settings/tiktok-business-connection.interface';

@Injectable({
  providedIn: 'root',
})
export class TikTokBusinessConnectionService {
  // Injects
  private httpClient = inject(HttpClient);
  private configService = inject(ConfigService);

  // State
  private readonly endpoint: string | undefined;

  constructor() {
    this.endpoint = this.configService.config?.environment.endpoint;
  }

  public index(
    page?: number,
  ): Observable<PaginatedResponse<TikTokBusinessConnection>> {
    let parameters = new HttpParams();

    if (page) {
      parameters = parameters.set('page', page);
    }

    return this.httpClient.get<PaginatedResponse<TikTokBusinessConnection>>(
      `${this.endpoint}/api/settings/connections/tiktok/business`,
      {
        params: parameters,
      },
    );
  }

  public destroy(tikTokAccountId: number): Observable<void> {
    return this.httpClient.delete<void>(
      `${this.endpoint}/api/settings/connections/tiktok/business/${tikTokAccountId}`,
    );
  }
}
