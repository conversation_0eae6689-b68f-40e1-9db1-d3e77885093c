import { inject, Injectable } from '@angular/core';
import { HttpClient, HttpParams } from '@angular/common/http';
import { Observable } from 'rxjs';
import { ConfigService } from '@services/config.service';
import { DataResponse } from '@responses/data.response';
import { MetadataResponse } from '@responses/metadata.response';
import { PaginatedResponse } from '@responses/paginated.response';
import { FacebookConnection } from '@api/interfaces/settings/facebook-connection.interface';
import { FacebookConnectionMetadata } from '@api/interfaces/settings/facebook-connection-metadata.interface';

@Injectable({
  providedIn: 'root',
})
export class FacebookConnectionService {
  // Injects
  private httpClient = inject(HttpClient);
  private configService = inject(ConfigService);

  // State
  private readonly endpoint: string | undefined;

  constructor() {
    this.endpoint = this.configService.config?.environment.endpoint;
  }

  public index(
    page?: number,
  ): Observable<PaginatedResponse<FacebookConnection>> {
    let parameters = new HttpParams();

    if (page) {
      parameters = parameters.set('page', page);
    }

    return this.httpClient.get<PaginatedResponse<FacebookConnection>>(
      `${this.endpoint}/api/settings/connections/facebook`,
      {
        params: parameters,
      },
    );
  }

  public show(
    facebookAccountId: number,
  ): Observable<
    DataResponse<FacebookConnection> &
      MetadataResponse<FacebookConnectionMetadata>
  > {
    return this.httpClient.get<
      DataResponse<FacebookConnection> &
        MetadataResponse<FacebookConnectionMetadata>
    >(
      `${this.endpoint}/api/settings/connections/facebook/${facebookAccountId}`,
    );
  }

  public destroy(facebookAccountId: number): Observable<void> {
    return this.httpClient.delete<void>(
      `${this.endpoint}/api/settings/connections/facebook/${facebookAccountId}`,
    );
  }
}
