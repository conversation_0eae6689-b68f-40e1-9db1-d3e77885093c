import { inject, Injectable } from '@angular/core';
import { HttpClient, HttpParams } from '@angular/common/http';
import { Observable } from 'rxjs';
import { ConfigService } from '@services/config.service';
import { DataResponse } from '@responses/data.response';
import { MetadataResponse } from '@responses/metadata.response';
import { PaginatedResponse } from '@responses/paginated.response';
import { LinkedInCommunityConnection } from '@api/interfaces/settings/linked-in-community-connection.interface';
import { LinkedInCommunityConnectionMetadata } from '@api/interfaces/settings/linked-in-community-connection-metadata.interface';

@Injectable({
  providedIn: 'root',
})
export class LinkedInCommunityConnectionService {
  // Injects
  private httpClient = inject(HttpClient);
  private configService = inject(ConfigService);

  // State
  private readonly endpoint: string | undefined;

  constructor() {
    this.endpoint = this.configService.config?.environment.endpoint;
  }

  public index(
    page?: number,
  ): Observable<PaginatedResponse<LinkedInCommunityConnection>> {
    let parameters = new HttpParams();

    if (page) {
      parameters = parameters.set('page', page);
    }

    return this.httpClient.get<PaginatedResponse<LinkedInCommunityConnection>>(
      `${this.endpoint}/api/settings/connections/linked_in/community`,
      {
        params: parameters,
      },
    );
  }

  public show(
    linkedInAccountId: number,
  ): Observable<
    DataResponse<LinkedInCommunityConnection> &
      MetadataResponse<LinkedInCommunityConnectionMetadata>
  > {
    return this.httpClient.get<
      DataResponse<LinkedInCommunityConnection> &
        MetadataResponse<LinkedInCommunityConnectionMetadata>
    >(
      `${this.endpoint}/api/settings/connections/linked_in/community/${linkedInAccountId}`,
    );
  }

  public destroy(linkedInAccountId: number): Observable<void> {
    return this.httpClient.delete<void>(
      `${this.endpoint}/api/settings/connections/linked_in/community/${linkedInAccountId}`,
    );
  }
}
