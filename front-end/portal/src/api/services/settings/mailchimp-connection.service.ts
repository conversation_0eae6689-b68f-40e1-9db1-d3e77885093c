import { inject, Injectable } from '@angular/core';
import { HttpClient, HttpParams } from '@angular/common/http';
import { Observable } from 'rxjs';
import { ConfigService } from '@services/config.service';
import { PaginatedResponse } from '@responses/paginated.response';
import { MailchimpConnection } from '@api/interfaces/settings/mailchimp-connection.interface';
import { MailchimpRequest } from '@api/requests/settings/mailchimp.request';

@Injectable({
  providedIn: 'root',
})
export class MailchimpConnectionService {
  // Injects
  private httpClient = inject(HttpClient);
  private configService = inject(ConfigService);

  // State
  private readonly endpoint: string | undefined;

  constructor() {
    this.endpoint = this.configService.config?.environment.endpoint;
  }

  public index(
    page?: number,
  ): Observable<PaginatedResponse<MailchimpConnection>> {
    let parameters = new HttpParams();

    if (page) {
      parameters = parameters.set('page', page);
    }

    return this.httpClient.get<PaginatedResponse<MailchimpConnection>>(
      `${this.endpoint}/api/settings/connections/mailchimp`,
      {
        params: parameters,
      },
    );
  }

  public destroy(mailchimpAccountId: number): Observable<void> {
    return this.httpClient.delete<void>(
      `${this.endpoint}/api/settings/connections/mailchimp/${mailchimpAccountId}`,
    );
  }

  public store(body: MailchimpRequest): Observable<void> {
    return this.httpClient.post<void>(
      `${this.endpoint}/api/settings/connections/mailchimp`,
      body,
    );
  }
}
