import { inject, Injectable } from '@angular/core';
import { HttpClient, HttpParams } from '@angular/common/http';
import { Observable } from 'rxjs';
import { ConfigService } from '@services/config.service';
import { DataResponse } from '@responses/data.response';
import { MetadataResponse } from '@responses/metadata.response';
import { PaginatedResponse } from '@responses/paginated.response';
import { YoutubeConnection } from '@api/interfaces/settings/youtube-connection.interface';
import { YoutubeConnectionMetadata } from '@api/interfaces/settings/youtube-connection-metadata.interface';

@Injectable({
  providedIn: 'root',
})
export class YoutubeConnectionService {
  // Injects
  private httpClient = inject(HttpClient);
  private configService = inject(ConfigService);

  // State
  private readonly endpoint: string | undefined;

  constructor() {
    this.endpoint = this.configService.config?.environment.endpoint;
  }

  public index(
    page?: number,
  ): Observable<PaginatedResponse<YoutubeConnection>> {
    let parameters = new HttpParams();

    if (page) {
      parameters = parameters.set('page', page);
    }

    return this.httpClient.get<PaginatedResponse<YoutubeConnection>>(
      `${this.endpoint}/api/settings/connections/youtube`,
      {
        params: parameters,
      },
    );
  }

  public show(
    youtubeGoogleAccountId: number,
  ): Observable<
    DataResponse<YoutubeConnection> &
      MetadataResponse<YoutubeConnectionMetadata>
  > {
    return this.httpClient.get<
      DataResponse<YoutubeConnection> &
        MetadataResponse<YoutubeConnectionMetadata>
    >(
      `${this.endpoint}/api/settings/connections/youtube/${youtubeGoogleAccountId}`,
    );
  }

  public destroy(youtubeGoogleAccountId: number): Observable<void> {
    return this.httpClient.delete<void>(
      `${this.endpoint}/api/settings/connections/youtube/${youtubeGoogleAccountId}`,
    );
  }
}
