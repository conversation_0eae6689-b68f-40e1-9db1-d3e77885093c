import { inject, Injectable } from '@angular/core';
import { HttpClient, HttpParams } from '@angular/common/http';
import { Observable } from 'rxjs';
import { ConfigService } from '@services/config.service';
import { DataResponse } from '@responses/data.response';
import { MetadataResponse } from '@responses/metadata.response';
import { PaginatedResponse } from '@responses/paginated.response';
import { GoogleConnection } from '@api/interfaces/settings/google-connection.interface';
import { GoogleConnectionMetadata } from '@api/interfaces/settings/google-connection-metadata.interface';

@Injectable({
  providedIn: 'root',
})
export class GoogleConnectionService {
  // Injects
  private httpClient = inject(HttpClient);
  private configService = inject(ConfigService);

  // State
  private readonly endpoint: string | undefined;

  constructor() {
    this.endpoint = this.configService.config?.environment.endpoint;
  }

  public index(page?: number): Observable<PaginatedResponse<GoogleConnection>> {
    let parameters = new HttpParams();

    if (page) {
      parameters = parameters.set('page', page);
    }

    return this.httpClient.get<PaginatedResponse<GoogleConnection>>(
      `${this.endpoint}/api/settings/connections/google`,
      {
        params: parameters,
      },
    );
  }

  public show(
    googleAccountId: number,
  ): Observable<
    DataResponse<GoogleConnection> & MetadataResponse<GoogleConnectionMetadata>
  > {
    return this.httpClient.get<
      DataResponse<GoogleConnection> &
        MetadataResponse<GoogleConnectionMetadata>
    >(`${this.endpoint}/api/settings/connections/google/${googleAccountId}`);
  }

  public destroy(googleAccountId: number): Observable<void> {
    return this.httpClient.delete<void>(
      `${this.endpoint}/api/settings/connections/google/${googleAccountId}`,
    );
  }
}
