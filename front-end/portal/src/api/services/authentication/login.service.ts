import { inject, Injectable } from '@angular/core';
import { HttpClient } from '@angular/common/http';
import { Observable } from 'rxjs';
import { ConfigService } from '@services/config.service';
import { DataResponse } from '@responses/data.response';
import { User } from '@api/interfaces/authentication/user.interface';
import { MetadataResponse } from '@responses/metadata.response';
import { UserMetadata } from '@api/interfaces/authentication/user-metadata.interface';

@Injectable({
  providedIn: 'root',
})
export class LoginService {
  // Injects
  private httpClient = inject(HttpClient);
  private configService = inject(ConfigService);

  // State
  private readonly endpoint: string | undefined;

  constructor() {
    this.endpoint = this.configService.config?.environment.endpoint;
  }

  public login(
    email: string,
    password: string,
  ): Observable<{ two_factor_required: boolean }> {
    return this.httpClient.post<{ two_factor_required: boolean }>(
      `${this.endpoint}/api/auth/login`,
      {
        email,
        password,
      },
    );
  }

  public requestPassword(email: string): Observable<DataResponse<User>> {
    return this.httpClient.post<DataResponse<User>>(
      `${this.endpoint}/api/auth/password/request`,
      {
        email,
      },
    );
  }

  public resetPassword(
    email: string,
    password: string,
    token: string,
  ): Observable<DataResponse<User>> {
    return this.httpClient.post<DataResponse<User>>(
      `${this.endpoint}/api/auth/password/reset`,
      {
        email,
        password,
        password_confirmation: password,
        token,
      },
    );
  }

  public me(): Observable<DataResponse<User> & MetadataResponse<UserMetadata>> {
    return this.httpClient.get<
      DataResponse<User> & MetadataResponse<UserMetadata>
    >(`${this.endpoint}/api/auth/me`);
  }

  public csrf(): Observable<void> {
    return this.httpClient.get<void>(`${this.endpoint}/sanctum/csrf-cookie`);
  }

  public logout(): Observable<void> {
    return this.httpClient.get<void>(`${this.endpoint}/api/auth/logout`, {});
  }
}
