import { inject, Injectable } from '@angular/core';
import { HttpClient, HttpParams } from '@angular/common/http';
import { Observable } from 'rxjs';
import { ConfigService } from '@services/config.service';
import { DataResponse } from '@responses/data.response';
import { User } from '@api/interfaces/authentication/user.interface';
import { MetadataResponse } from '@responses/metadata.response';
import { UserMetadata } from '@api/interfaces/authentication/user-metadata.interface';
import { PaginatedResponse } from '@responses/paginated.response';

@Injectable({
  providedIn: 'root',
})
export class UserService {
  // Injects
  private httpClient = inject(HttpClient);
  private configService = inject(ConfigService);

  // State
  private readonly endpoint: string | undefined;

  constructor() {
    this.endpoint = this.configService.config?.environment.endpoint;
  }

  public index(page?: number): Observable<PaginatedResponse<User>> {
    let parameters = new HttpParams();

    if (page) {
      parameters = parameters.set('page', page);
    }

    return this.httpClient.get<PaginatedResponse<User>>(
      `${this.endpoint}/api/user`,
      {
        params: parameters,
      },
    );
  }

  public show(
    id: number,
  ): Observable<DataResponse<User> & MetadataResponse<UserMetadata>> {
    return this.httpClient.get<
      DataResponse<User> & MetadataResponse<UserMetadata>
    >(`${this.endpoint}/api/user/${id}`);
  }

  public store(data: Partial<User>): Observable<DataResponse<User>> {
    return this.httpClient.post<DataResponse<User>>(
      `${this.endpoint}/api/user`,
      {
        name: data.name,
        email: data.email,
        role: data.role,
        dashboards: data.dashboards,
      },
    );
  }

  public update(
    userId: number,
    data: Partial<User>,
  ): Observable<DataResponse<User>> {
    return this.httpClient.patch<DataResponse<User>>(
      `${this.endpoint}/api/user/${userId}`,
      {
        name: data.name,
        email: data.email,
        role: data.role,
        dashboards: data.dashboards,
      },
    );
  }

  public destroy(userId: number): Observable<void> {
    return this.httpClient.delete<void>(`${this.endpoint}/api/user/${userId}`);
  }
}
