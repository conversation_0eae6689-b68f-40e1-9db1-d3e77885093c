import { inject, Injectable } from '@angular/core';
import { HttpClient } from '@angular/common/http';
import { Observable } from 'rxjs';
import { ConfigService } from '@services/config.service';
import { DataResponse } from '@responses/data.response';
import { User } from '@api/interfaces/authentication/user.interface';

@Injectable({
  providedIn: 'root',
})
export class TwoFactorService {
  // Injects
  private httpClient = inject(HttpClient);
  private configService = inject(ConfigService);

  // State
  private readonly endpoint: string | undefined;

  constructor() {
    this.endpoint = this.configService.config?.environment.endpoint;
  }

  public verify(code: string): Observable<DataResponse<User>> {
    return this.httpClient.post<DataResponse<User>>(
      `${this.endpoint}/api/auth/2fa`,
      {
        code,
      },
    );
  }
}
