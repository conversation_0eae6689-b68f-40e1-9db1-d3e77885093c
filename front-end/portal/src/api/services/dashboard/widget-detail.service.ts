import { inject, Injectable } from '@angular/core';
import { HttpClient } from '@angular/common/http';
import { Observable } from 'rxjs';
import { ConfigService } from '@services/config.service';
import { DataResponse } from '@responses/data.response';
import { WidgetAnalytics } from '@api/interfaces/dashboard/widget-analytics.interface';
import { FilterState } from '@api/interfaces/dashboard/filter-state.interface';
import { dashboardFilterStateToHttpParams } from '@api/helpers/DashboardFilterStateToHttpParams';
import { WidgetDetailScope } from '@api/enums/dashboard/widget-detail-scope.enum';

@Injectable({
  providedIn: 'root',
})
export class WidgetDetailService {
  // Injects
  private httpClient = inject(HttpClient);
  private configService = inject(ConfigService);

  // State
  private readonly endpoint: string | undefined;

  constructor() {
    this.endpoint = this.configService.config?.environment.endpoint;
  }

  public statistics(
    id: number,
    widgetDetailScope: WidgetDetailScope,
    filters?: FilterState,
  ): Observable<DataResponse<{ [label: string]: WidgetAnalytics }>> {
    const endpoint = `${this.endpoint}/api/dashboard/widget/${id}/detail/statistics/${widgetDetailScope}`;

    return this.httpClient.get<
      DataResponse<{ [label: string]: WidgetAnalytics }>
    >(endpoint, {
      params: dashboardFilterStateToHttpParams(filters),
    });
  }
}
