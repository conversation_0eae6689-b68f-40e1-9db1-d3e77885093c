import { inject, Injectable } from '@angular/core';
import { HttpClient } from '@angular/common/http';
import { Observable } from 'rxjs';
import { ConfigService } from '@services/config.service';
import { Widget } from '@api/interfaces/dashboard/widget.interface';

@Injectable({
  providedIn: 'root',
})
export class WidgetTargetService {
  // Injects
  private httpClient = inject(HttpClient);
  private configService = inject(ConfigService);

  // State
  private readonly endpoint: string | undefined;

  constructor() {
    this.endpoint = this.configService.config?.environment.endpoint;
  }

  public update(widgetId: number, target: number): Observable<Widget> {
    return this.httpClient.patch<Widget>(
      `${this.endpoint}/api/dashboard/widget/${widgetId}/target`,
      { target },
    );
  }

  public destroy(widgetId: number): Observable<{ message: string }> {
    return this.httpClient.delete<{ message: string }>(
      `${this.endpoint}/api/dashboard/widget/${widgetId}/target`,
    );
  }
}
