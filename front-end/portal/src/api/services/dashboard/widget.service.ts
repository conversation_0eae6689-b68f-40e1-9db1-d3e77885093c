import { inject, Injectable } from '@angular/core';
import { HttpClient } from '@angular/common/http';
import { Observable } from 'rxjs';
import { ConfigService } from '@services/config.service';
import { DataResponse } from '@responses/data.response';
import { WidgetAnalytics } from '@api/interfaces/dashboard/widget-analytics.interface';
import { FilterState } from '@api/interfaces/dashboard/filter-state.interface';
import { dashboardFilterStateToHttpParams } from '@api/helpers/DashboardFilterStateToHttpParams';
import { WidgetPostAnalytics } from '@api/interfaces/dashboard/widget-post-analytics.interface';

@Injectable({
  providedIn: 'root',
})
export class WidgetService {
  // Injects
  private httpClient = inject(HttpClient);
  private configService = inject(ConfigService);

  // State
  private readonly endpoint: string | undefined;

  constructor() {
    this.endpoint = this.configService.config?.environment.endpoint;
  }

  public statistics(
    id: number,
    filters?: FilterState,
  ): Observable<DataResponse<WidgetAnalytics | WidgetPostAnalytics>> {
    const endpoint = `${this.endpoint}/api/dashboard/widget/${id}/statistics`;

    return this.httpClient.get<
      DataResponse<WidgetAnalytics | WidgetPostAnalytics>
    >(endpoint, {
      params: dashboardFilterStateToHttpParams(filters),
    });
  }
}
