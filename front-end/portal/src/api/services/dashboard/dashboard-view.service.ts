import { inject, Injectable } from '@angular/core';
import { HttpClient } from '@angular/common/http';
import { Observable } from 'rxjs';
import { ConfigService } from '@services/config.service';
import { FilterState } from '@api/interfaces/dashboard/filter-state.interface';
import { dashboardFilterStateToHttpParams } from '@api/helpers/DashboardFilterStateToHttpParams';

@Injectable({
  providedIn: 'root',
})
export class DashboardViewService {
  // Injects
  private httpClient = inject(HttpClient);
  private configService = inject(ConfigService);

  // State
  private readonly endpoint: string | undefined;

  constructor() {
    this.endpoint = this.configService.config?.environment.endpoint;
  }

  public store(
    dashboardId: number,
    name: string,
    filters: FilterState,
  ): Observable<void> {
    const params = dashboardFilterStateToHttpParams(filters);

    return this.httpClient.post<void>(
      `${this.endpoint}/api/dashboard/${dashboardId}/view`,
      {
        name: name,
        start_date: params.get('start_date'),
        end_date: params.get('end_date'),
        channels: params.get('channels'),
        regions: params.get('regions'),
        business_units: params.get('business_units'),
      },
    );
  }

  public destroy(dashboardId: number, viewId: number): Observable<void> {
    return this.httpClient.delete<void>(
      `${this.endpoint}/api/dashboard/${dashboardId}/view/${viewId}`,
    );
  }
}
