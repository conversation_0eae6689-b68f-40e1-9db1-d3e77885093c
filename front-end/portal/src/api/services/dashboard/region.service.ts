import { inject, Injectable } from '@angular/core';
import { ConfigService } from '@services/config.service';
import { HttpClient } from '@angular/common/http';
import { Observable } from 'rxjs';
import { DataResponse } from '@responses/data.response';
import { PaginatedResponse } from '@responses/paginated.response';
import { Region } from '@api/interfaces/dashboard/region.interface';
import { RegionRequest } from '@api/requests/dashboard/region.request';
import { IndexRequest } from '@api/requests/support/index.request';
import { paramsToHttpParams } from '../../../app/helpers/transform-params';

@Injectable({
  providedIn: 'root',
})
export class RegionService {
  private readonly endpoint?: string;

  private configService = inject(ConfigService);
  private httpClient = inject(HttpClient);

  constructor() {
    this.endpoint = this.configService.config?.environment.endpoint;
  }

  public index(
    params: IndexRequest,
  ): Observable<PaginatedResponse<Region> | DataResponse<Region[]>> {
    return this.httpClient.get<
      PaginatedResponse<Region> | DataResponse<Region[]>
    >(`${this.endpoint}/api/settings/regions`, {
      params: paramsToHttpParams(params),
    });
  }

  public show(id: number): Observable<DataResponse<Region>> {
    return this.httpClient.get<DataResponse<Region>>(
      `${this.endpoint}/api/settings/regions/${id}`,
    );
  }

  public store(body: RegionRequest): Observable<DataResponse<Region>> {
    return this.httpClient.post<DataResponse<Region>>(
      `${this.endpoint}/api/settings/regions`,
      body,
    );
  }

  public update(
    businessUnit: Region,
    body: RegionRequest,
  ): Observable<DataResponse<Region>> {
    return this.httpClient.put<DataResponse<Region>>(
      `${this.endpoint}/api/settings/regions/${businessUnit.id}`,
      body,
    );
  }
}
