import { inject, Injectable } from '@angular/core';
import { HttpClient } from '@angular/common/http';
import { Observable } from 'rxjs';
import { ConfigService } from '@services/config.service';
import { DataResponse } from '@responses/data.response';
import { Dashboard } from '@api/interfaces/dashboard/dashboard.interface';
import { MetadataResponse } from '@responses/metadata.response';
import { DashboardMetadata } from '@api/interfaces/dashboard/dashboard-metadata.interface';
import { IndexRequest } from '@api/requests/support/index.request';
import { paramsToHttpParams } from '../../../app/helpers/transform-params';
import { PaginatedResponse } from '@responses/paginated.response';
import { DashboardRequest } from '@api/requests/dashboard/dashboard.request';

@Injectable({
  providedIn: 'root',
})
export class DashboardService {
  // Injects
  private httpClient = inject(HttpClient);
  private configService = inject(ConfigService);

  // State
  private readonly endpoint: string | undefined;

  constructor() {
    this.endpoint = this.configService.config?.environment.endpoint;
  }

  public index(
    params: IndexRequest,
  ): Observable<DataResponse<Dashboard[]> | PaginatedResponse<Dashboard>> {
    return this.httpClient.get<
      DataResponse<Dashboard[]> | PaginatedResponse<Dashboard>
    >(`${this.endpoint}/api/dashboard`, {
      params: paramsToHttpParams(params),
    });
  }

  public show(
    id: number,
  ): Observable<DataResponse<Dashboard> & MetadataResponse<DashboardMetadata>> {
    return this.httpClient.get<
      DataResponse<Dashboard> & MetadataResponse<DashboardMetadata>
    >(`${this.endpoint}/api/dashboard/${id}`);
  }

  public store(body: DashboardRequest): Observable<DataResponse<Dashboard>> {
    return this.httpClient.post<DataResponse<Dashboard>>(
      `${this.endpoint}/api/settings/dashboards`,
      body,
    );
  }

  public update(
    dashboard: Dashboard,
    body: DashboardRequest,
  ): Observable<DataResponse<Dashboard>> {
    return this.httpClient.put<DataResponse<Dashboard>>(
      `${this.endpoint}/api/settings/dashboards/${dashboard.id}`,
      body,
    );
  }
}
