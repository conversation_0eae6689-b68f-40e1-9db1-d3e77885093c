import { HttpClient } from '@angular/common/http';
import { inject, Injectable } from '@angular/core';
import { dashboardFilterStateToHttpParams } from '@api/helpers/DashboardFilterStateToHttpParams';
import { FilterState } from '@api/interfaces/dashboard/filter-state.interface';
import { PostAnalytics } from '@api/interfaces/dashboard/post-analytics.interface';
import { DataResponse } from '@responses/data.response';
import { ConfigService } from '@services/config.service';
import { Observable } from 'rxjs';

@Injectable({
  providedIn: 'root',
})
export class PostAnalyticsService {
  // Injects
  private httpClient = inject(HttpClient);
  private configService = inject(ConfigService);

  // State
  private readonly endpoint: string | undefined;

  constructor() {
    this.endpoint = this.configService.config?.environment.endpoint;
  }

  public statistics(
    id: number,
    postId: number,
    filters?: FilterState,
  ): Observable<DataResponse<PostAnalytics>> {
    const endpoint = `${this.endpoint}/api/dashboard/widget/${id}/detail/posts/${postId}/statistics/`;

    return this.httpClient.get<DataResponse<PostAnalytics>>(endpoint, {
      params: dashboardFilterStateToHttpParams(filters),
    });
  }
}
