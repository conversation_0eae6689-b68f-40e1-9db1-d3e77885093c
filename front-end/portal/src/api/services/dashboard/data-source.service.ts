import { inject, Injectable } from '@angular/core';
import { ConfigService } from '@services/config.service';
import { HttpClient } from '@angular/common/http';
import { DataSourceIndexRequest } from '@api/requests/dashboard/data-source-index.request';
import { Observable } from 'rxjs';
import { PaginatedResponse } from '@responses/paginated.response';
import { DataSource } from '@api/interfaces/dashboard/data-source.interface';
import { paramsToHttpParams } from '../../../app/helpers/transform-params';
import { DataSourceSource } from '@api/interfaces/dashboard/data-source-source.interface';
import { DataResponse } from '@responses/data.response';
import { DataSourceType } from '@api/enums/dashboard/data-source-type.enum';
import {
  DataSourceStoreRequest,
  DataSourceUpdateRequest,
} from '@api/requests/dashboard/data-source.request';
import { DataSourceAllowedTypes } from '@api/interfaces/dashboard/data-source-allowed-types.interface';

@Injectable({
  providedIn: 'root',
})
export class DataSourceService {
  private readonly endpoint?: string;

  private configService = inject(ConfigService);
  private httpClient = inject(HttpClient);

  constructor() {
    this.endpoint = this.configService.config?.environment.endpoint;
  }

  public index(
    request: DataSourceIndexRequest,
  ): Observable<PaginatedResponse<DataSource>> {
    const params = paramsToHttpParams(request);

    return this.httpClient.get<PaginatedResponse<DataSource>>(
      `${this.endpoint}/api/settings/data-sources`,
      {
        params,
      },
    );
  }

  public sources(
    type: DataSourceType,
    page: number,
    search: string | null = null,
  ): Observable<PaginatedResponse<DataSourceSource>> {
    return this.httpClient.get<PaginatedResponse<DataSourceSource>>(
      `${this.endpoint}/api/settings/data-sources/sources`,
      {
        params: paramsToHttpParams({ page, search, type }),
      },
    );
  }

  public show(id: number): Observable<DataResponse<DataSource>> {
    return this.httpClient.get<DataResponse<DataSource>>(
      `${this.endpoint}/api/settings/data-sources/${id}`,
    );
  }

  public store(body: DataSourceStoreRequest): Observable<void> {
    return this.httpClient.post<void>(
      `${this.endpoint}/api/settings/data-sources`,
      body,
    );
  }

  public update(
    dataSource: DataSource,
    body: DataSourceUpdateRequest,
  ): Observable<void> {
    return this.httpClient.put<void>(
      `${this.endpoint}/api/settings/data-sources/${dataSource.id}`,
      body,
    );
  }

  public allowedTypes(): Observable<DataResponse<DataSourceAllowedTypes>> {
    return this.httpClient.get<DataResponse<DataSourceAllowedTypes>>(
      `${this.endpoint}/api/settings/data-sources/allowed-types`,
    );
  }
}
