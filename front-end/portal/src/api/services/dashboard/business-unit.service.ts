import { inject, Injectable } from '@angular/core';
import { ConfigService } from '@services/config.service';
import { HttpClient } from '@angular/common/http';
import { Observable } from 'rxjs';
import { DataResponse } from '@responses/data.response';
import { BusinessUnit } from '@api/interfaces/dashboard/business-unit.interface';
import { BusinessUnitRequest } from '@api/requests/dashboard/business-unit.request';
import { PaginatedResponse } from '@responses/paginated.response';
import { IndexRequest } from '@api/requests/support/index.request';
import { paramsToHttpParams } from '../../../app/helpers/transform-params';

@Injectable({
  providedIn: 'root',
})
export class BusinessUnitService {
  private readonly endpoint?: string;

  private configService = inject(ConfigService);
  private httpClient = inject(HttpClient);

  constructor() {
    this.endpoint = this.configService.config?.environment.endpoint;
  }

  public index(
    params: IndexRequest,
  ): Observable<
    PaginatedResponse<BusinessUnit> | DataResponse<BusinessUnit[]>
  > {
    return this.httpClient.get<
      PaginatedResponse<BusinessUnit> | DataResponse<BusinessUnit[]>
    >(`${this.endpoint}/api/settings/business_units`, {
      params: paramsToHttpParams(params),
    });
  }

  public show(id: number): Observable<DataResponse<BusinessUnit>> {
    return this.httpClient.get<DataResponse<BusinessUnit>>(
      `${this.endpoint}/api/settings/business_units/${id}`,
    );
  }

  public store(
    body: BusinessUnitRequest,
  ): Observable<DataResponse<BusinessUnit>> {
    return this.httpClient.post<DataResponse<BusinessUnit>>(
      `${this.endpoint}/api/settings/business_units`,
      body,
    );
  }

  public update(
    businessUnit: BusinessUnit,
    body: BusinessUnitRequest,
  ): Observable<DataResponse<BusinessUnit>> {
    return this.httpClient.put<DataResponse<BusinessUnit>>(
      `${this.endpoint}/api/settings/business_units/${businessUnit.id}`,
      body,
    );
  }
}
