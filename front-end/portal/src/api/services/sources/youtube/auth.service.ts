import { inject, Injectable } from '@angular/core';
import { HttpClient } from '@angular/common/http';
import { Observable } from 'rxjs';
import { ConfigService } from '@services/config.service';
import { DataResponse } from '@responses/data.response';

@Injectable({
  providedIn: 'root',
})
export class YoutubeAuthService {
  // Injects
  private httpClient = inject(HttpClient);
  private configService = inject(ConfigService);

  private readonly endpoint: string | undefined;

  constructor() {
    this.endpoint = this.configService.config?.environment.endpoint;
  }

  public connect(): Observable<DataResponse<{ url: string }>> {
    return this.httpClient.get<DataResponse<{ url: string }>>(
      `${this.endpoint}/api/sources/youtube/connect`,
    );
  }
}
