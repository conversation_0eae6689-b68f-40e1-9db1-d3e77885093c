import { DataSourceType } from '@api/enums/dashboard/data-source-type.enum';

export interface DataSourceRequest {
  type: DataSourceType;
  region: string;
  business_unit: string;
  dashboard_ids: number[];
}

export interface DataSourceStoreRequest extends DataSourceRequest {
  sources: {
    id: number;
    title: string;
  }[];
}

export interface DataSourceUpdateRequest extends DataSourceRequest {
  title: string;
}
