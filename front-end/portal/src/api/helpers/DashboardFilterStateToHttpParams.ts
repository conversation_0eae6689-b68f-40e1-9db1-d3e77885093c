import { HttpParams } from '@angular/common/http';
import formatDate from 'date-fns/format';
import { FilterState } from '@api/interfaces/dashboard/filter-state.interface';

export const dashboardFilterStateToHttpParams = (filters?: FilterState) => {
  let parameters = new HttpParams();

  if (filters?.startDate) {
    let startDate = formatDate(filters.startDate, 'yyyy-MM-dd');
    parameters = parameters.set('start_date', startDate);
  }
  if (filters?.endDate) {
    let endDate = formatDate(filters.endDate, 'yyyy-MM-dd');
    parameters = parameters.set('end_date', endDate);
  }
  if (filters?.channels) {
    let channels = filters.channels.join(',');
    parameters = parameters.set('channels', channels);
  }
  if (filters?.regions) {
    let regions = filters.regions.join(',');
    parameters = parameters.set('regions', regions);
  }
  if (filters?.businessUnits) {
    let businessUnits = filters.businessUnits.join(',');
    parameters = parameters.set('business_units', businessUnits);
  }

  return parameters;
};
