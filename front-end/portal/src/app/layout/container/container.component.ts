import { ChangeDetectionStrategy, Component, inject } from '@angular/core';
import { CommonModule } from '@angular/common';
import { Router, RouterModule, RouterOutlet } from '@angular/router';
import { TranslocoDirective } from '@jsverse/transloco';
import { AuthService } from '@services/auth.service';
import { ConfigService } from '@services/config.service';

@Component({
  selector: 'layout-container',
  templateUrl: './container.component.html',
  standalone: true,
  imports: [CommonModule, RouterOutlet, RouterModule, TranslocoDirective],
  changeDetection: ChangeDetectionStrategy.OnPush,
})
export class ContainerComponent {
  // State
  public isMobileMenuOpen = false;

  // Inject
  private router = inject(Router);
  protected authService = inject(AuthService);
  protected configService = inject(ConfigService);

  public toggleMobileMenu() {
    this.isMobileMenuOpen = !this.isMobileMenuOpen;
  }

  public logOut(): void {
    try {
      this.authService.logout();
    } catch (error) {}

    this.router.navigateByUrl('/auth/login');
  }
}
