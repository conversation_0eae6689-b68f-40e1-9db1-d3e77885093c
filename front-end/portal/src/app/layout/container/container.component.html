<div *transloco="let t; prefix: 'layout.container'" class="min-h-full">
  <nav class="bg-lkq-pure-white shadow-sm sticky top-0 z-50">
    <div class="mx-auto max-w-7xl px-4 sm:px-6 lg:px-8">
      <div class="flex h-16 justify-between">
        <div class="flex">
          <div class="flex flex-shrink-0 items-center">
            @if (configService.config?.environment?.brandStyle === 'lkq') {
              <img class="block w-24 h-auto" src="images/logo.svg" alt="Logo">
            } @else {
              <img class="block w-28 h-auto" src="images/toads.png" alt="Logo">
            }
          </div>
          <div class="hidden sm:-my-px sm:ml-6 sm:flex sm:space-x-8">
            <ng-container *ngTemplateOutlet="desktopMenuItems"></ng-container>
          </div>
        </div>
        <div class="hidden sm:ml-6 sm:flex sm:items-center">
          <div (click)="logOut()" class="block px-4 py-2 text-base font-medium text-lkq-gray-800 hover:bg-lkq-gray-100 hover:text-lkq-gray-900 cursor-pointer">
            {{ t('sign_out') }}
          </div>
        </div>
        <div class="-mr-2 flex items-center sm:hidden">
          <ng-container *ngTemplateOutlet="mobileMenuButton"></ng-container>
        </div>
      </div>
    </div>

    <div *ngIf="isMobileMenuOpen" class="sm:hidden">
      <div class="space-y-1 pb-3 pt-2">
        <ng-container *ngTemplateOutlet="mobileMenuItems"></ng-container>
      </div>
    </div>
  </nav>

  <div class="pb-10">
    <router-outlet></router-outlet>
  </div>
</div>

<ng-template #mobileMenuButton>
  <button (click)="toggleMobileMenu()" type="button" class="relative inline-flex items-center justify-center rounded-md bg-lkq-pure-white p-2 text-lkq-gray-800 hover:bg-lkq-gray-100 hover:text-lkq-gray-900 focus:outline-none focus:ring-2 focus:ring-lkq-electric-blue focus:ring-offset-2" aria-controls="mobile-menu" aria-expanded="false">
    <span class="absolute -inset-0.5"></span>
    <span class="sr-only">Open main menu</span>
    <svg class="h-6 w-6" [class.block]="!isMobileMenuOpen" [class.hidden]="isMobileMenuOpen" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor" aria-hidden="true">
      <path stroke-linecap="round" stroke-linejoin="round" d="M3.75 6.75h16.5M3.75 12h16.5m-16.5 5.25h16.5" />
    </svg>
    <svg class="h-6 w-6" [class.block]="isMobileMenuOpen" [class.hidden]="!isMobileMenuOpen" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor" aria-hidden="true">
      <path stroke-linecap="round" stroke-linejoin="round" d="M6 18L18 6M6 6l12 12" />
    </svg>
  </button>
</ng-template>

<ng-template #mobileMenuItems>
  <ng-container *transloco="let t; prefix: 'layout.container'">
    <a
      routerLink="/dashboard"
      routerLinkActive="!border-lkq-electric-blue !bg-lkq-blue-200 !text-lkq-pure-white"
      class="block border-transparent border-l-4 py-2 pl-3 pr-4 text-base font-medium text-lkq-gray-800 hover:border-lkq-gray-200 hover:bg-lkq-gray-100 hover:text-lkq-gray-800"
    >
      {{ t('menu.dashboard') }}
    </a>
    <a
      routerLink="/settings"
      routerLinkActive="!border-lkq-electric-blue !bg-lkq-blue-200 !text-lkq-pure-white"
      class="block border-transparent border-l-4 py-2 pl-3 pr-4 text-base font-medium text-lkq-gray-800 hover:border-lkq-gray-200 hover:bg-lkq-gray-100 hover:text-lkq-gray-800"
    >
      {{ t('menu.settings') }}
    </a>
  </ng-container>
</ng-template>

<ng-template #desktopMenuItems>
  <ng-container *transloco="let t; prefix: 'layout.container'">
    <a
      routerLink="/dashboard"
      routerLinkActive="!border-lkq-electric-blue !text-lkq-gray-900"
      class="inline-flex items-center border-transparent text-lkq-gray-800 hover:border-lkq-gray-500 hover:text-lkq-gray-800 border-b-2 px-1 pt-1 text-sm font-medium"
    >
      {{ t('menu.dashboard') }}
    </a>
    @if (authService.isAdmin()) {
      <a
        routerLink="/settings"
        routerLinkActive="!border-lkq-electric-blue !text-lkq-gray-900"
        class="inline-flex items-center border-transparent text-lkq-gray-800 hover:border-lkq-gray-500 hover:text-lkq-gray-800 border-b-2 px-1 pt-1 text-sm font-medium"
      >
        {{ t('menu.settings') }}
      </a>
    }
  </ng-container>
</ng-template>
