import { inject, Injectable } from '@angular/core';
import { LoginService } from '@api/services/authentication/login.service';
import { User } from '@api/interfaces/authentication/user.interface';
import { Role } from '@api/enums/authentication/role.enum';
import { UserMetadata } from '@api/interfaces/authentication/user-metadata.interface';

@Injectable({
  providedIn: 'root',
})
export class AuthService {
  // Injects
  private loginService = inject(LoginService);

  public setUser(user: User, metadata: UserMetadata): void {
    localStorage.setItem('logged_in', 'true');
    localStorage.setItem('user', JSON.stringify(user));
    localStorage.setItem('metadata', JSON.stringify(metadata));
  }

  public getUser(): User | null {
    const rawUser = localStorage.getItem('user');
    if (!rawUser) {
      return null;
    }

    const data = JSON.parse(rawUser) as unknown;
    return data as User;
  }

  public getMetadata(): UserMetadata | null {
    const rawMetadata = localStorage.getItem('metadata');
    if (!rawMetadata) {
      return null;
    }

    const data = JSON.parse(rawMetadata) as unknown;
    return data as UserMetadata;
  }

  public isLoggedIn(): boolean {
    return !!localStorage.getItem('logged_in');
  }

  public isAdmin(): boolean {
    const user = this.getUser();
    return (
      !!user?.role &&
      [Role.ADMIN, Role.SUPER_ADMIN, Role.MASTER_ADMIN].includes(user.role)
    );
  }

  public isSuperAdmin(): boolean {
    const user = this.getUser();
    return (
      !!user?.role && [Role.MASTER_ADMIN, Role.SUPER_ADMIN].includes(user.role)
    );
  }

  public isMasterAdmin(): boolean {
    const user = this.getUser();
    return !!user?.role && [Role.MASTER_ADMIN].includes(user.role);
  }

  public isUser(): boolean {
    return this.getUser()?.role === Role.USER;
  }

  public isViewManager(): boolean {
    return this.getUser()?.role === Role.VIEW_MANAGER;
  }

  public logout(): void {
    localStorage.removeItem('logged_in');
    this.loginService.logout().subscribe();
  }
}
