import { inject, Injectable } from '@angular/core';
import { RouterStateSnapshot, TitleStrategy } from '@angular/router';
import { Title } from '@angular/platform-browser';
import { ConfigService } from '@services/config.service';

@Injectable({ providedIn: 'root' })
export class PageTitleStrategy extends TitleStrategy {
  // Injects
  private configService = inject(ConfigService);

  constructor(private readonly title: Title) {
    super();
  }
  override updateTitle(routerState: RouterStateSnapshot) {
    const title = this.buildTitle(routerState);
    let brand = 'Fource';

    if (this.configService.config?.environment.brandStyle === 'toads') {
      brand = 'Toads';
    }

    if (title !== undefined) {
      this.title.setTitle(`${title} | ${brand}`);
    }
  }
}
