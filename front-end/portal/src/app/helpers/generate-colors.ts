export const generateColors = (count: number): string[] => {
  const colors: any[] = [];
  const minDistance = 80; // Minimum color distance

  function colorDistance(color1: any, color2: any) {
    const r1 = parseInt(color1.slice(1, 3), 16);
    const g1 = parseInt(color1.slice(3, 5), 16);
    const b1 = parseInt(color1.slice(5, 7), 16);

    const r2 = parseInt(color2.slice(1, 3), 16);
    const g2 = parseInt(color2.slice(3, 5), 16);
    const b2 = parseInt(color2.slice(5, 7), 16);

    return Math.sqrt(
      Math.pow(r2 - r1, 2) + Math.pow(g2 - g1, 2) + Math.pow(b2 - b1, 2),
    );
  }

  function hslToHex(h: any, s: any, l: any) {
    l /= 100;
    const a = (s * Math.min(l, 1 - l)) / 100;
    const f = (n: any) => {
      const k = (n + h / 30) % 12;
      const color = l - a * Math.max(Math.min(k - 3, 9 - k, 1), -1);
      return Math.round(255 * color)
        .toString(16)
        .padStart(2, '0');
    };
    return `#${f(0)}${f(8)}${f(4)}`;
  }

  for (let i = 0; i < count; i++) {
    let attempts = 0;
    let newColor: any;

    do {
      const hue = ((i * 360) / count + attempts * 13) % 360;
      const saturation = 60 + (attempts % 3) * 15;
      const lightness = 50 + (attempts % 4) * 10;

      newColor = hslToHex(hue, saturation, lightness);
      attempts++;
    } while (
      colors.some(
        (existingColor) => colorDistance(newColor, existingColor) < minDistance,
      ) &&
      attempts < 100
    );

    colors.push(newColor);
  }

  return colors;
};
