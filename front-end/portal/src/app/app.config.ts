import {
  APP_INITIALIZER,
  ApplicationConfig,
  provideZoneChangeDetection,
  isDevMode,
  importProvidersFrom,
} from '@angular/core';
import {
  provideRouter,
  RouteReuseStrategy,
  TitleStrategy,
  withComponentInputBinding,
  withRouterConfig,
} from '@angular/router';
import { routes } from './app.routes';
import { ConfigService } from '@services/config.service';
import { provideHttpClient, withInterceptors } from '@angular/common/http';
import { TranslocoHttpLoader } from './transloco-loader';
import { provideTransloco, TranslocoService } from '@jsverse/transloco';
import { PageTitleStrategy } from './page-title-strategy';
import { firstValueFrom } from 'rxjs';
import { nl_NL, provideNzI18n } from 'ng-zorro-antd/i18n';
import { registerLocaleData } from '@angular/common';
import nl from '@angular/common/locales/nl';
import { FormsModule } from '@angular/forms';
import { provideAnimationsAsync } from '@angular/platform-browser/animations/async';
import { provideTranslocoMessageformat } from '@jsverse/transloco-messageformat';
import { unauthenticatedInterceptor } from '@interceptors/unauthenticated.interceptor';
import { RouteReuseStrategy as CustomRouteReuseStrategy } from './route-reuse-strategy';

registerLocaleData(nl);

const DEFAULT_LANGUAGE = 'en';

export function preloadTranslation(transloco: TranslocoService) {
  return function () {
    transloco.setActiveLang(DEFAULT_LANGUAGE);
    return firstValueFrom(transloco.load(DEFAULT_LANGUAGE));
  };
}

export const appConfig: ApplicationConfig = {
  providers: [
    { provide: TitleStrategy, useClass: PageTitleStrategy },
    {
      provide: APP_INITIALIZER,
      useFactory: (configService: ConfigService) => () =>
        configService.initialize(),
      deps: [ConfigService],
      multi: true,
    },
    provideAnimationsAsync(),
    provideZoneChangeDetection({ eventCoalescing: true }),
    provideRouter(
      routes,
      withRouterConfig({ onSameUrlNavigation: 'reload' }),
      withComponentInputBinding(),
    ),
    { provide: RouteReuseStrategy, useClass: CustomRouteReuseStrategy },
    provideHttpClient(withInterceptors([unauthenticatedInterceptor])),
    provideTransloco({
      config: {
        availableLangs: [DEFAULT_LANGUAGE],
        defaultLang: DEFAULT_LANGUAGE,
        reRenderOnLangChange: true,
        prodMode: !isDevMode(),
        missingHandler: {
          logMissingKey: false,
        },
      },
      loader: TranslocoHttpLoader,
    }),
    {
      provide: APP_INITIALIZER,
      multi: true,
      deps: [TranslocoService],
      useFactory: preloadTranslation,
    },
    provideTranslocoMessageformat(),
    provideNzI18n(nl_NL),
    importProvidersFrom(FormsModule),
  ],
};
