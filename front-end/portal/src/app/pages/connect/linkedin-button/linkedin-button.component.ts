import {
  ChangeDetectionStrategy,
  Component,
  inject,
  Input,
} from '@angular/core';
import { lastValueFrom } from 'rxjs';
import { LinkedInAuthService } from '@api/services/sources/linkedin/auth.service';

@Component({
  selector: 'pages-connect-linkedin-button',
  templateUrl: './linkedin-button.component.html',
  standalone: true,
  imports: [],
  changeDetection: ChangeDetectionStrategy.OnPush,
})
export class LinkedinButtonComponent {
  // Inputs
  @Input() type!: 'linkedin-ads' | 'linkedin-community';

  // Injects
  private linkedInAuthService = inject(LinkedInAuthService);

  public async connect(): Promise<void> {
    const service = {
      'linkedin-ads': this.linkedInAuthService.connectAds(),
      'linkedin-community': this.linkedInAuthService.connectCommunity(),
    };

    const response = await lastValueFrom(service[this.type]);

    if (!response?.data?.url) {
      return;
    }

    window.location.href = response?.data?.url;
  }
}
