import {
  ChangeDetectionStrategy,
  Component,
  inject,
  Input,
} from '@angular/core';
import { GoogleAuthService } from '@api/services/sources/google/auth.service';
import { lastValueFrom } from 'rxjs';
import { ConfigService } from '@services/config.service';

@Component({
  selector: 'pages-connect-google-button',
  templateUrl: './google-button.component.html',
  styleUrls: ['google-button.component.scss'],
  standalone: true,
  imports: [],
  changeDetection: ChangeDetectionStrategy.OnPush,
})
export class GoogleButtonComponent {
  @Input() isBusinessProfiles: boolean = false;

  // Injects
  private googleAuthService = inject(GoogleAuthService);
  private configService = inject(ConfigService);

  public async connect(): Promise<void> {
    if (
      this.isBusinessProfiles &&
      this.configService.config?.environment.googleBpUrl
    ) {
      window.location.href = this.configService.config.environment.googleBpUrl;
      return;
    }

    const response = await lastValueFrom(this.googleAuthService.connect());

    if (!response?.data?.url) {
      return;
    }

    window.location.href = response?.data?.url;
  }
}
