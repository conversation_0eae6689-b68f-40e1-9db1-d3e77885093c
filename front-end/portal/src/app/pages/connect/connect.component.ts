import {
  ChangeDetectionStrategy,
  Component,
  inject,
  OnInit,
} from '@angular/core';
import { TranslocoDirective } from '@jsverse/transloco';
import { GoogleButtonComponent } from './google-button/google-button.component';
import { ActivatedRoute, Router } from '@angular/router';
import { FacebookButtonComponent } from './facebook-button/facebook-button.component';
import { YoutubeGoogleButtonComponent } from './youtube-google-button/youtube-google-button.component';
import { LinkedinButtonComponent } from './linkedin-button/linkedin-button.component';
import { TiktokOrganicButtonComponent } from './tiktok-organic-button/tiktok-organic-button.component';
import { TiktokBusinessButtonComponent } from './tiktok-business-button/tiktok-business-button.component';
import { ConfigService } from '@services/config.service';

type State = 'prompt' | 'success' | 'missing_scopes';
type Provider =
  | 'google'
  | 'youtube'
  | 'facebook'
  | 'tiktok-organic'
  | 'tiktok-business'
  | 'linkedin-ads'
  | 'linkedin-community';

interface QueryParams {
  state?: State;
  provider?: Provider;
}

@Component({
  selector: 'pages-connect',
  templateUrl: './connect.component.html',
  standalone: true,
  imports: [
    TranslocoDirective,
    GoogleButtonComponent,
    FacebookButtonComponent,
    YoutubeGoogleButtonComponent,
    LinkedinButtonComponent,
    TiktokOrganicButtonComponent,
    TiktokBusinessButtonComponent,
  ],
  changeDetection: ChangeDetectionStrategy.OnPush,
})
export class ConnectComponent implements OnInit {
  // State
  public connectionState?: State;
  public providerType?: Provider;

  // Injects
  private route = inject(ActivatedRoute);
  private router = inject(Router);
  protected configService = inject(ConfigService);

  public ngOnInit(): void {
    this.readRouteParameters();
  }

  protected readRouteParameters(): void {
    this.route.queryParams.subscribe((params: QueryParams) => {
      if (!params.provider) {
        this.router.navigateByUrl('/');
        return;
      }

      this.connectionState = params.state ?? 'prompt';
      this.providerType = params.provider;
    });
  }
}
