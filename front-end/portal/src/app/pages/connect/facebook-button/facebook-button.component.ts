import { ChangeDetectionStrategy, Component, inject } from '@angular/core';
import { FacebookAuthService } from '@api/services/sources/facebook/auth.service';
import { lastValueFrom } from 'rxjs';

@Component({
  selector: 'pages-connect-facebook-button',
  templateUrl: './facebook-button.component.html',
  standalone: true,
  imports: [],
  changeDetection: ChangeDetectionStrategy.OnPush,
})
export class FacebookButtonComponent {
  // Injects
  private facebookAuthService = inject(FacebookAuthService);

  public async connect(): Promise<void> {
    const response = await lastValueFrom(this.facebookAuthService.connect());

    if (!response?.data?.url) {
      return;
    }

    window.location.href = response?.data?.url;
  }
}
