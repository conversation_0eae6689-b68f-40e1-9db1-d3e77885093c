import { ChangeDetectionStrategy, Component, inject } from '@angular/core';
import { lastValueFrom } from 'rxjs';
import { YoutubeAuthService } from '@api/services/sources/youtube/auth.service';

@Component({
  selector: 'pages-connect-youtube-google-button',
  templateUrl: './youtube-google-button.component.html',
  styleUrls: ['youtube-google-button.component.scss'],
  standalone: true,
  imports: [],
  changeDetection: ChangeDetectionStrategy.OnPush,
})
export class YoutubeGoogleButtonComponent {
  // Injects
  private youtubeAuthService = inject(YoutubeAuthService);

  public async connect(): Promise<void> {
    const response = await lastValueFrom(this.youtubeAuthService.connect());

    if (!response?.data?.url) {
      return;
    }

    window.location.href = response?.data?.url;
  }
}
