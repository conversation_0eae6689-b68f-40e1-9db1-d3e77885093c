import { ChangeDetectionStrategy, Component, inject } from '@angular/core';
import { lastValueFrom } from 'rxjs';
import { TiktokOrganicAuthService } from '@api/services/sources/tiktok-organic/auth.service';

@Component({
  selector: 'pages-connect-tiktok-organic-button',
  templateUrl: './tiktok-organic-button.component.html',
  standalone: true,
  imports: [],
  changeDetection: ChangeDetectionStrategy.OnPush,
})
export class TiktokOrganicButtonComponent {
  // Injects
  private tiktokOrganicAuthService = inject(TiktokOrganicAuthService);

  public async connect(): Promise<void> {
    const response = await lastValueFrom(
      this.tiktokOrganicAuthService.connect(),
    );

    if (!response?.data?.url) {
      return;
    }

    window.location.href = response?.data?.url;
  }
}
