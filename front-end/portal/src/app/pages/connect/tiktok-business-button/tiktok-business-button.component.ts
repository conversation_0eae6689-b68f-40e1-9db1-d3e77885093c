import { ChangeDetectionStrategy, Component, inject } from '@angular/core';
import { lastValueFrom } from 'rxjs';
import { TiktokBusinessAuthService } from '@api/services/sources/tiktok-business/auth.service';

@Component({
  selector: 'pages-connect-tiktok-business-button',
  templateUrl: './tiktok-business-button.component.html',
  standalone: true,
  imports: [],
  changeDetection: ChangeDetectionStrategy.OnPush,
})
export class TiktokBusinessButtonComponent {
  // Injects
  private tiktokBusinessAuthService = inject(TiktokBusinessAuthService);

  public async connect(): Promise<void> {
    const response = await lastValueFrom(
      this.tiktokBusinessAuthService.connect(),
    );

    if (!response?.data?.url) {
      return;
    }

    window.location.href = response?.data?.url;
  }
}
