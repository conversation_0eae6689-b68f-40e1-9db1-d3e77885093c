<div *transloco="let t; prefix: 'pages.connect'" class="mx-auto max-w-7xl px-4 sm:px-6 lg:px-8 pt-10">
  <div class="mx-auto max-w-xl py-12">
    @if (configService.config?.environment?.brandStyle === 'lkq') {
      <img class="mx-auto h-10 w-auto" src="images/logo.svg" alt="Logo">
    } @else {
      <img class="mx-auto h-10 w-auto" src="images/toads.png" alt="Logo">
    }

    <div class="bg-lkq-pure-white shadow sm:rounded-lg max-w-xl mt-6">
      <div class="px-4 py-5 sm:px-6">
        <h2 class="text-lg font-medium leading-6 text-lkq-gray-900">
          {{ t('state.' + connectionState + '.title') }}
        </h2>
        <p class="mt-1 max-w-2xl text-sm text-lkq-gray-800">
          {{ t('state.' + connectionState + '.description') }}
        </p>
      </div>
      @if (connectionState !== 'success') {
        <div class="text-center px-4 py-5 sm:px-6">
          @if (providerType === 'google') {
            <pages-connect-google-button/>
          }
          @if (providerType === 'youtube') {
            <pages-connect-youtube-google-button/>
          }
          @if (providerType === 'facebook') {
            <pages-connect-facebook-button/>
          }
          @if (providerType === 'tiktok-organic') {
            <pages-connect-tiktok-organic-button/>
          }
          @if (providerType === 'tiktok-business') {
            <pages-connect-tiktok-business-button/>
          }
          @if (providerType === 'linkedin-ads' || providerType === 'linkedin-community') {
            <pages-connect-linkedin-button [type]="providerType"/>
          }
        </div>
      }
    </div>
  </div>
</div>
