import { inject, Injectable } from '@angular/core';
import { lastValueFrom, Subject } from 'rxjs';
import { Dashboard } from '@api/interfaces/dashboard/dashboard.interface';
import { DashboardService } from '@api/services/dashboard/dashboard.service';
import { FilterState } from '@api/interfaces/dashboard/filter-state.interface';
import { DashboardMetadata } from '@api/interfaces/dashboard/dashboard-metadata.interface';
import { subDays } from 'date-fns';
import { DashboardView } from '@api/interfaces/dashboard/dashboard-view.interface';

@Injectable({
  providedIn: 'root',
})
export class DashboardState {
  // Injects
  private dashboardService = inject(DashboardService);

  // State
  public refresh$ = new Subject<void>();
  public dashboard?: Dashboard;
  public dashboardView?: DashboardView;
  public dashboardMetadata?: DashboardMetadata;
  public filterState?: FilterState;

  protected setInitialFilterState(): void {
    if (this.filterState) {
      return;
    }

    this.filterState = {
      startDate: subDays(new Date(), 31),
      endDate: subDays(new Date(), 1),
    };
  }

  public async load(id: number): Promise<void> {
    this.setInitialFilterState();
    const response = await lastValueFrom(this.dashboardService.show(id));

    this.dashboard = response.data;
    this.dashboardMetadata = response.metadata;
    this.dashboardView = undefined;
  }

  public async refresh(
    id: number | undefined = undefined,
    dashboardView?: DashboardView,
  ): Promise<void> {
    if (!this.dashboard) {
      return;
    }

    await this.load(id ?? this.dashboard.id);

    this.dashboardView = dashboardView;
    this.refresh$.next();
  }

  public updateFilters(state: Partial<FilterState>): void {
    this.filterState = {
      ...this.filterState,
      ...state,
    };
  }
}
