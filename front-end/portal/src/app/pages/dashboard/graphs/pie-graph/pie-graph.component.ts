import { Component, EventEmitter, Input, OnInit, Output } from '@angular/core';
import { FilterState } from '@api/interfaces/dashboard/filter-state.interface';
import { WidgetEventAnalytics } from '@api/interfaces/dashboard/widget-event-analytics.interface';
import {
  ApexChart,
  ApexPlotOptions,
  ApexResponsive,
  ApexTooltip,
  NgApexchartsModule,
} from 'ng-apexcharts';
import { generateColors } from '../../../../helpers/generate-colors';
import { NgClass } from '@angular/common';

export type PieChartOptions = {
  series: number[];
  chart: ApexChart;
  tooltip: ApexTooltip;
  labels: string[];
  responsive: ApexResponsive[];
  plotOptions: ApexPlotOptions;
  colors: string[];
};

@Component({
  selector: 'dashboard-statistics-pie-graph',
  standalone: true,
  imports: [NgApexchartsModule, NgClass],
  templateUrl: './pie-graph.component.html',
})
export class PieGraphComponent implements OnInit {
  @Input({ required: true }) public title!: string;
  @Input({ required: true }) public inverseComparisonIndicator: boolean = false;
  @Input({ required: true }) public widgetEventAnalytics!: WidgetEventAnalytics;
  @Input({ required: true }) public filterState: FilterState | undefined;
  @Input() public hideButtons: boolean = false;

  @Output() public detailViewClicked = new EventEmitter<void>();

  public chartOptions!: PieChartOptions;

  public ngOnInit(): void {
    this.setChartOptions();
  }

  private setChartOptions(): void {
    const data = this.getMappedData();

    this.chartOptions = {
      series: data.series,
      colors: generateColors(data.series.length),
      tooltip: {
        custom: function ({ seriesIndex }) {
          // Get the additional data from your original data array
          const comparison = data.comparison[seriesIndex];
          const label = data.labels[seriesIndex];
          const value = data.series[seriesIndex];
          const valueFormatted = data.valuesFormatted[seriesIndex];
          const comparisonFormatted = data.comparisonsFormatted[seriesIndex];
          const difference = value - comparison;
          const percentChange = ((difference / comparison) * 100).toFixed(1);

          let template = `
            <div class="bg-white border border-gray-200 rounded-lg shadow-lg overflow-hidden p-3 min-w-[200px]">
              <div class="font-semibold text-gray-900 text-sm border-b border-gray-200 pb-2 mb-2">
                ${label}
              </div>
              <div class="space-y-1">
                <div class="flex justify-between text-sm">
                  <span class="text-gray-600">Amount:</span>
                  <span class="font-medium text-gray-900">${valueFormatted}</span>
                </div>
          `;

          if (comparison > 0) {
            template += `
              <div class="flex justify-between text-sm">
                <span class="text-gray-600">Previous period:</span>
                <span class="font-medium text-gray-900">${comparisonFormatted}</span>
              </div>
              <div class="flex justify-between text-sm">
                <span class="text-gray-600">Difference:</span>
                <span class="font-medium ${difference >= 0 ? 'text-green-600' : 'text-red-600'}">
                  ${difference} (${percentChange}%)
                </span>
              </div>
            `;
          }

          template += `</div></div>`;

          return template;
        },
      },
      chart: {
        width: 380,
        type: 'pie',
      },
      labels: data.labels,
      responsive: [
        {
          breakpoint: 480,
          options: {
            chart: {
              width: 200,
            },
            legend: {
              position: 'bottom',
            },
          },
        },
      ],
      plotOptions: {
        pie: {
          expandOnClick: false,
        },
      },
    };
  }

  private getMappedData(): {
    series: number[];
    labels: string[];
    comparison: number[];
    valuesFormatted: string[];
    comparisonsFormatted: string[];
  } {
    const series: number[] = [];
    const labels: string[] = [];
    const comparison: number[] = [];
    const valuesFormatted: string[] = [];
    const comparisonsFormatted: string[] = [];

    this.widgetEventAnalytics.values
      .sort((a, b) => b.value - a.value)
      .forEach((event) => {
        series.push(event.value);
        labels.push(event.name);
        comparison.push(event.comparison ? event.comparison : 0);
        valuesFormatted.push(event.valueFormatted);
        comparisonsFormatted.push(
          event.comparisonFormatted ? event.comparisonFormatted : '',
        );
      });

    return {
      series,
      labels,
      comparison,
      valuesFormatted,
      comparisonsFormatted,
    };
  }

  public formattedComparisonPercentage(
    widgetEventAnalytics: WidgetEventAnalytics,
  ): {
    value: number;
    type: 'higher' | 'lower' | 'unchanged';
  } {
    const percentage =
      ((widgetEventAnalytics.total - widgetEventAnalytics.comparison) /
        widgetEventAnalytics.comparison) *
      100;

    let type: 'higher' | 'lower' | 'unchanged' = 'unchanged';
    if (percentage < 0) {
      type = 'lower';
    }
    if (percentage > 0) {
      type = 'higher';
    }

    return {
      value: Math.round(Math.abs(percentage) * 10) / 10,
      type,
    };
  }
}
