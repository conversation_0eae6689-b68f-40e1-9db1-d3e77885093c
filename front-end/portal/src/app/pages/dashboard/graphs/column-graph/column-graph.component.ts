import {
  ChangeDetectionStrategy,
  Component,
  EventEmitter,
  Input,
  OnInit,
  Output,
} from '@angular/core';
import {
  ApexAxisChartSeries,
  ApexChart,
  ApexDataLabels,
  ApexFill,
  ApexTooltip,
  ApexXAxis,
  ApexYAxis,
  NgApexchartsModule,
  ApexAnnotations,
} from 'ng-apexcharts';
import { WidgetAnalytics } from '@api/interfaces/dashboard/widget-analytics.interface';
import {
  addDays,
  addMonths,
  differenceInDays,
  format,
  startOfMonth,
} from 'date-fns';
import { WidgetAccuracy } from '@api/enums/dashboard/widget-accuracy.enum';
import { FilterState } from '@api/interfaces/dashboard/filter-state.interface';
import { formatNumber, NgClass } from '@angular/common';

export type ChartOptions = {
  series: ApexAxisChartSeries;
  chart: ApexChart;
  tooltip: ApexTooltip;
  dataLabels: ApexDataLabels;
  xaxis: ApexXAxis;
  yaxis: ApexYAxis;
  fill: ApexFill;
  annotations: ApexAnnotations;
};

@Component({
  selector: 'dashboard-statistics-column-graph',
  templateUrl: './column-graph.component.html',
  standalone: true,
  imports: [NgApexchartsModule, NgClass],
  changeDetection: ChangeDetectionStrategy.OnPush,
})
export class ColumnGraphComponent implements OnInit {
  // Inputs
  @Input({ required: true }) public title!: string;
  @Input({ required: true }) public color!: string;
  @Input({ required: true }) public inverseComparisonIndicator: boolean = false;
  @Input({ required: true }) public widgetAnalytics!: WidgetAnalytics;
  @Input({ required: true }) public dataType!: 'number' | 'percentage' | 'euro';
  @Input({ required: true }) public filterState: FilterState | undefined;
  @Input() public target: number | null = null;
  @Input() public hideButtons: boolean = false;

  // Outputs
  @Output() public detailViewClicked = new EventEmitter<void>();
  @Output() public targetViewClicked = new EventEmitter<void>();

  // State
  public chartOptions?: ChartOptions;

  public ngOnInit() {
    this.setChartOptions();
  }

  private setChartOptions(): void {
    const accuracy = this.widgetAnalytics.accuracy;
    const dateFormat = accuracy === 'DAY' ? 'dd/MM' : 'MMM yyyy';

    const yAxisConfig = this.calculateYAxisTicks();

    this.chartOptions = {
      fill: {
        colors: ['#fff'],
      },
      dataLabels: {
        enabled: false,
      },
      series: this.chartSeries(),
      tooltip: {
        x: {
          formatter: (val: number) => format(new Date(val), dateFormat),
        },
      },
      chart: {
        height: '100%',
        width: '100%',
        type: 'bar',
        toolbar: {
          show: false,
        },
        zoom: {
          enabled: false,
        },
        selection: {
          enabled: false,
        },
      },
      xaxis: {},
      yaxis: {
        ...yAxisConfig,
      },
      annotations: {
        yaxis: this.getTargetAnnotation(),
      },
    };

    if (this.dataType === 'number') {
      this.chartOptions.yaxis = {
        ...this.chartOptions.yaxis,
        labels: {
          formatter: (value) => formatNumber(value, 'nl', '1.0-0'),
        },
      };
    }

    if (this.dataType === 'percentage') {
      this.chartOptions.yaxis = {
        ...this.chartOptions.yaxis,
        labels: {
          formatter: (value) => formatNumber(value, 'nl', '1.1-1') + '%',
        },
      };
    }

    if (this.dataType === 'euro') {
      this.chartOptions.yaxis = {
        ...this.chartOptions.yaxis,
        labels: {
          formatter: (value) => '€ ' + formatNumber(value, 'nl', '1.2-2'),
        },
      };
    }

    if (accuracy === WidgetAccuracy.MONTH) {
      this.chartOptions.xaxis.type = 'category';
      this.chartOptions.xaxis.labels = {
        ...this.chartOptions.xaxis.labels,
        formatter: (val: string) => format(new Date(val), dateFormat),
      };
    }

    if (accuracy === WidgetAccuracy.DAY) {
      this.chartOptions.xaxis.type = 'datetime';
      this.chartOptions.xaxis.labels = {
        ...this.chartOptions.xaxis.labels,
        format: dateFormat,
      };
    }
  }

  private calculateYAxisTicks(targetTicks = 6): Partial<ApexYAxis> {
    // Round min down and max up to get clean values
    const values = this.getMinAndMaxValues();
    let minValue = values.min;
    let maxValue = values.max;

    // Calculate the data range
    let range = maxValue - minValue;

    // Determine if we need to use decimals
    // Only use decimals if both min and max are below 1
    const useDecimals = maxValue < 1;

    // Calculate an appropriate tick interval based on range
    let tickInterval;

    // For both cases, find an appropriate interval
    const magnitude = Math.pow(10, Math.floor(Math.log10(range / targetTicks)));
    const candidateIntervals = [
      magnitude,
      2 * magnitude,
      5 * magnitude,
      10 * magnitude,
    ];

    // Find the interval that gives us closest to targetTicks number of steps
    let bestInterval = candidateIntervals[0];
    let bestDiff = Math.abs(range / bestInterval - targetTicks);

    for (let i = 1; i < candidateIntervals.length; i++) {
      const diff = Math.abs(range / candidateIntervals[i] - targetTicks);
      if (diff < bestDiff) {
        bestDiff = diff;
        bestInterval = candidateIntervals[i];
      }
    }

    tickInterval = bestInterval;

    // Determine precision for formatting (number of decimal places)
    let precision = 0; // Default to integers

    if (useDecimals) {
      // Always use exactly one decimal place when decimals are needed
      precision = 1;
    }

    // Adjust min and max to be clean multiples of the tick interval
    minValue = Math.floor(minValue / tickInterval) * tickInterval;
    maxValue = Math.ceil(maxValue / tickInterval) * tickInterval;

    // Generate tick values
    const tickValues = [];
    for (
      let i = minValue;
      i <= maxValue + tickInterval / 2;
      i += tickInterval
    ) {
      if (useDecimals) {
        tickValues.push(parseFloat(i.toFixed(precision)));
      } else {
        // For non-decimal ranges, ensure we have integer ticks
        tickValues.push(Math.round(i));
      }
    }

    return {
      min: minValue,
      max: maxValue,
      tickAmount: undefined,
      forceNiceScale: false,
      decimalsInFloat: useDecimals ? precision : 0,
      labels: {
        formatter: function (val) {
          return useDecimals
            ? val.toFixed(precision)
            : Math.round(val).toString();
        },
      },
    };
  }

  private getMinAndMaxValues(): { min: number; max: number } {
    // Get the max and min values from data
    const dataValues = this.widgetAnalytics.values.map((item) => item.value);
    const maxDataValue = dataValues.length ? Math.max(...dataValues) : 0;
    const minDataValue = dataValues.length ? Math.min(...dataValues) : 0;

    // Calculate min value (10% below lowest value or target, whichever is lower)
    let effectiveMinValue = minDataValue;
    if (this.target !== null) {
      effectiveMinValue = Math.min(minDataValue, this.target);
    }

    // Skip min adjustment for percentage or if min value is already 0
    const minPadding = effectiveMinValue > 0 ? effectiveMinValue * 0.1 : 0;
    const min = Math.max(effectiveMinValue - minPadding, 0); // Don't go below zero

    // Calculate the effective maximum value based on data type
    if (this.dataType === 'percentage') {
      // For percentage, use the higher of max data value or target
      const effectiveMaxValue = this.target
        ? Math.max(maxDataValue, this.target)
        : maxDataValue;

      // Add 10% padding above the effective max value
      const padding = effectiveMaxValue * 0.1;
      const max = effectiveMaxValue + padding;

      // Return the calculated max, but cap it at 100 if it's only slightly above
      return { min: 0, max: Math.min(max, 100) };
    }

    // For non-percentage data types
    // If no target or target is less than max data value, use default behavior
    if (!this.target || this.target <= maxDataValue) {
      return { min, max: maxDataValue };
    }

    // Add padding above the target line (20% of target value)
    const padding = this.target * 0.2;
    const max = this.target + padding;

    return { min, max };
  }

  private getTargetAnnotation(): {
    y: number;
    borderColor: string;
    label: { text: string; style: { color: string } };
  }[] {
    if (!this.target) {
      return [];
    }

    let formattedTarget = this.target.toString();
    if (this.dataType === 'percentage') {
      formattedTarget = this.target.toFixed(1) + '%';
    } else if (this.dataType === 'euro') {
      formattedTarget = '€ ' + this.target.toFixed(2);
    }

    return [
      {
        y: this.target,
        borderColor: '#FF4560',
        label: {
          text: `Target: ${formattedTarget}`,
          style: {
            color: '#FF4560',
          },
        },
      },
    ];
  }

  private chartSeries(): ApexAxisChartSeries {
    return [
      {
        name: this.title,
        data: this.parsedData(),
      },
    ];
  }

  private parsedData(): { x: string; y: number }[] {
    const accuracy = this.widgetAnalytics.accuracy;
    const dateFormat =
      accuracy === WidgetAccuracy.DAY ? 'yyyy-MM-dd' : 'yyyy-MM-01';

    // Sort the values by date to ensure chronological order
    const sortedValues = [...this.widgetAnalytics.values].sort(
      (a, b) => new Date(a.date).getTime() - new Date(b.date).getTime(),
    );

    if (accuracy === WidgetAccuracy.MONTH) {
      return this.generateCompleteMonthlyData(sortedValues, dateFormat);
    }

    // For daily view, ensure at least 7 days and no gaps
    return this.generateCompleteDailyData(sortedValues, dateFormat);
  }

  private generateCompleteMonthlyData(
    sortedValues: { date: string; value: number }[],
    dateFormat: string,
  ): { x: string; y: number }[] {
    if (!sortedValues.length) {
      return [];
    }

    // Determine start and end dates based on filter state
    let startDate: Date = startOfMonth(new Date(sortedValues[0].date));
    let endDate: Date = startOfMonth(
      new Date(sortedValues[sortedValues.length - 1].date),
    );

    if (this.filterState?.startDate && this.filterState?.endDate) {
      // Use filter state dates
      startDate = startOfMonth(this.filterState.startDate);
      endDate = startOfMonth(this.filterState.endDate);
    }

    // Create a map of existing data points for quick lookup
    const dataMap = new Map(
      sortedValues.map((point) => [
        format(startOfMonth(new Date(point.date)), dateFormat),
        point.value,
      ]),
    );

    const completeData: { x: string; y: number }[] = [];
    let currentDate = startDate;

    // Generate all months between start and end, filling gaps with zeros
    while (currentDate <= endDate) {
      const formattedDate = format(currentDate, dateFormat);
      completeData.push({
        x: formattedDate,
        y: dataMap.get(formattedDate) ?? 0,
      });
      currentDate = addMonths(currentDate, 1);
    }

    return completeData;
  }

  private generateCompleteDailyData(
    sortedValues: { date: string; value: number }[],
    dateFormat: string,
  ): { x: string; y: number }[] {
    if (!sortedValues.length) {
      return [];
    }

    // Get the data range boundaries
    const dataStartDate = new Date(sortedValues[0].date);
    const dataEndDate = new Date(sortedValues[sortedValues.length - 1].date);

    let startDate: Date = dataStartDate;
    let endDate: Date = dataEndDate;

    if (this.filterState?.startDate && this.filterState?.endDate) {
      // Use filter state dates
      startDate = this.filterState.startDate;
      endDate = this.filterState.endDate;

      // Calculate days in range
      const daysInRange = differenceInDays(endDate, startDate) + 1;

      // If less than 5 days, pad the end
      if (daysInRange < 1) {
        endDate = addDays(startDate, 4); // Makes it 5 days total
      }
    }

    // Create a map of existing data points for quick lookup
    const dataMap = new Map(
      sortedValues.map((point) => [
        format(new Date(point.date), dateFormat),
        point.value,
      ]),
    );

    const completeData: { x: string; y: number }[] = [];
    let currentDate = startDate;

    // Generate all dates between start and end, filling gaps with zeros
    while (currentDate <= endDate) {
      const formattedDate = format(currentDate, dateFormat);
      completeData.push({
        x: formattedDate,
        y: dataMap.get(formattedDate) ?? 0,
      });
      currentDate = addDays(currentDate, 1);
    }

    return completeData;
  }

  public formattedComparisonPercentage(widgetAnalytics: WidgetAnalytics): {
    value: number;
    type: 'higher' | 'lower' | 'unchanged';
  } {
    const percentage =
      ((widgetAnalytics.total - widgetAnalytics.comparison) /
        widgetAnalytics.comparison) *
      100;

    let type: 'higher' | 'lower' | 'unchanged' = 'unchanged';
    if (percentage < 0) {
      type = 'lower';
    }
    if (percentage > 0) {
      type = 'higher';
    }

    return {
      value: Math.round(Math.abs(percentage) * 10) / 10,
      type,
    };
  }
}
