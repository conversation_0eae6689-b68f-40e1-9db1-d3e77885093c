import {
  ChangeDetectionStrategy,
  Component,
  EventEmitter,
  Input,
  Output,
} from '@angular/core';
import { WidgetPostAnalytics } from '@api/interfaces/dashboard/widget-post-analytics.interface';
import { TranslocoDirective } from '@jsverse/transloco';
import { NgClass } from '@angular/common';

@Component({
  selector: 'dashboard-statistics-image-widget',
  templateUrl: './image-widget.component.html',
  standalone: true,
  imports: [TranslocoDirective, NgClass],
  changeDetection: ChangeDetectionStrategy.OnPush,
})
export class ImageWidgetComponent {
  // Inputs
  @Input({ required: true }) public title!: string;
  @Input({ required: true }) public color!: string;
  @Input({ required: true }) public inverseComparisonIndicator: boolean = false;
  @Input({ required: true }) public widgetPostAnalytics!: WidgetPostAnalytics;

  // Outputs
  @Output() public detailViewClicked = new EventEmitter<void>();

  public formattedComparisonPercentage(widgetAnalytics: WidgetPostAnalytics): {
    value: number;
    type: 'higher' | 'lower' | 'unchanged';
  } {
    const percentage =
      ((widgetAnalytics.total - widgetAnalytics.comparison) /
        widgetAnalytics.comparison) *
      100;

    let type: 'higher' | 'lower' | 'unchanged' = 'unchanged';
    if (percentage < 0) {
      type = 'lower';
    }
    if (percentage > 0) {
      type = 'higher';
    }

    return {
      value: Math.round(Math.abs(percentage) * 10) / 10,
      type,
    };
  }
}
