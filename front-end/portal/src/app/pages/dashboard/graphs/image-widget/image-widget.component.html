<div class="flex justify-between items-center">
  <div>
    <div [class.max-w-64]="widgetPostAnalytics.comparisonFormatted" [title]="title" class="text-xl font-medium text-lkq-gray-900 truncate">{{ title }}</div>
    <div class="text-base font-normal text-lkq-gray-800">
      <span>{{ widgetPostAnalytics.totalFormatted }}</span>
      @if(widgetPostAnalytics.comparisonFormatted) {
        <span class="text-xs text-gray-500 pl-2">(vs {{ widgetPostAnalytics.comparisonFormatted }})</span>
      }
    </div>
  </div>
  <div>
    <div class="min-h-7">
      @if(widgetPostAnalytics.comparisonFormatted) {
        @let statistics = formattedComparisonPercentage(widgetPostAnalytics);
        @if (statistics.type === 'lower') {
          <div
            [ngClass]="{
                'bg-lkq-sustainability-300 text-lkq-sustainability-spring-green': inverseComparisonIndicator,
                'bg-red-100 text-red-800': !inverseComparisonIndicator,
              }"
            class="inline-flex items-baseline rounded-full px-2.5 py-0.5 text-sm font-medium md:mt-2 lg:mt-0"
          >
            <svg
              [ngClass]="{
                'text-lkq-sustainability-spring-green': inverseComparisonIndicator,
                'text-red-500': !inverseComparisonIndicator
                }"
              class="-ml-1 mr-0.5 h-4 w-4 flex-shrink-0 self-center" viewBox="0 0 20 20" fill="currentColor" aria-hidden="true"
            >
              <path fill-rule="evenodd" d="M10 3a.75.75 0 01.75.75v10.638l3.96-4.158a.75.75 0 111.08 1.04l-5.25 5.5a.75.75 0 01-1.08 0l-5.25-5.5a.75.75 0 111.08-1.04l3.96 4.158V3.75A.75.75 0 0110 3z" clip-rule="evenodd" />
            </svg>
            {{ statistics.value }}%
          </div>
        }
        @if(statistics.type === 'higher') {
          <div
            [ngClass]="
                {
                  'bg-lkq-sustainability-300': !inverseComparisonIndicator,
                  'bg-red-100 text-red-800': inverseComparisonIndicator
                }
              "
            class="inline-flex items-baseline rounded-full px-2.5 py-0.5 text-sm font-medium md:mt-2 lg:mt-0"
          >
            <svg
              [class.text-red-500]="inverseComparisonIndicator"
              class="-ml-1 mr-0.5 h-4 w-4 flex-shrink-0 self-center text-lkq-sustainability-spring-green" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor"
            >
              <path stroke-linecap="round" stroke-linejoin="round" d="M4.5 10.5 12 3m0 0 7.5 7.5M12 3v18" />
            </svg>
            {{ statistics.value }}%
          </div>
        }
        @if(statistics.type === 'unchanged') {
          <div class="inline-flex items-baseline rounded-full bg-lkq-gray-100 px-2.5 py-0.5 text-sm font-medium text-lkq-gray-800 md:mt-2 lg:mt-0">
            <svg class="-ml-1 mr-0.5 h-5 w-5 flex-shrink-0 self-center text-lkq-gray-500" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor">
              <path stroke-linecap="round" stroke-linejoin="round" d="M5 12h14" />
            </svg>
            {{ statistics.value }}%
          </div>
        }
      }
    </div>
    @if (widgetPostAnalytics.post.url || widgetPostAnalytics.post.text) {
      <div class="flex justify-end">
        <!-- Detail view -->
        <button
          class="p-1 rounded-full hover:bg-gray-100 transition-colors"
          (click)="detailViewClicked.emit()">
          <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="currentColor" class="h-5 w-5 text-gray-500">
            <path d="M11.625 16.5a1.875 1.875 0 1 0 0-3.75 1.875 1.875 0 0 0 0 3.75Z" />
            <path fill-rule="evenodd" d="M5.625 1.5H9a3.75 3.75 0 0 1 3.75 3.75v1.875c0 1.036.84 1.875 1.875 1.875H16.5a3.75 3.75 0 0 1 3.75 3.75v7.875c0 1.035-.84 1.875-1.875 1.875H5.625a1.875 1.875 0 0 1-1.875-1.875V3.375c0-1.036.84-1.875 1.875-1.875Zm6 16.5c.66 0 1.277-.19 1.797-.518l1.048 1.048a.75.75 0 0 0 1.06-1.06l-1.047-1.048A3.375 3.375 0 1 0 11.625 18Z" clip-rule="evenodd" />
            <path d="M14.25 5.25a5.23 5.23 0 0 0-1.279-3.434 9.768 9.768 0 0 1 6.963 6.963A5.23 5.23 0 0 0 16.5 7.5h-1.875a.375.375 0 0 1-.375-.375V5.25Z" />
          </svg>
        </button>
        <!-- /Detail view -->
      </div>
    }
  </div>
</div>
<div *transloco="let t;">
  <div class="pt-4">
    <div class="relative max-h-52 h-52 overflow-hidden rounded-lg">
      @if (widgetPostAnalytics.post.url) {
        <img class="object-contain w-full h-full" src="{{ widgetPostAnalytics.post.url }}" alt="Post Image">
      }
      @if (! widgetPostAnalytics.post.url && widgetPostAnalytics.post.text) {
        <div class="text-center text-gray-900 my-auto flex items-center justify-center h-full bg-[#E8EFF4]">{{ widgetPostAnalytics.post.text }}</div>
      }
      @if (!widgetPostAnalytics.post.text && !widgetPostAnalytics.post.url) {
        <div class="text-center text-gray-500">{{ t('pages.dashboard.no_data') }}</div>
      }
    </div>
  </div>
</div>
