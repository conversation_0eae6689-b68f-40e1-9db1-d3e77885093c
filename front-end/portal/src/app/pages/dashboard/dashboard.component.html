<ng-container *transloco="let t; prefix: 'pages.dashboard'">
  @if(dashboardState.dashboard) {
    <header class="sticky top-[4.03rem] bg-lkq-gray-200 z-50 pt-10 pb-2">
      <div class="flex flex-col space-y-4 items-center justify-between mx-auto max-w-7xl px-4 sm:px-6 lg:px-8">
        <div class="flex justify-between w-full">
          <div class="w-24"></div>
          <div class="flex flex-col items-center">
            <h1 class="text-3xl font-medium leading-tight tracking-tight text-lkq-gray-900">
              {{ dashboardState.dashboard.title }}
            </h1>
            <h3 class="leading-tight tracking-tight text-lkq-gray-800 h-5">
              {{ dashboardState.dashboardView?.name }}
            </h3>
          </div>
          <div class="w-24 flex justify-end space-x-2">
            <!-- Select Dashboard -->
            <div (click)="openDashboardPickerModal()" class="cursor-pointer text-lkq-electric-blue hover:text-lkq-gray-800">
              <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="currentColor" class="size-6">
                <path fill-rule="evenodd" d="M5.625 1.5H9a3.75 3.75 0 0 1 3.75 3.75v1.875c0 1.036.84 1.875 1.875 1.875H16.5a3.75 3.75 0 0 1 3.75 3.75v7.875c0 1.035-.84 1.875-1.875 1.875H5.625a1.875 1.875 0 0 1-1.875-1.875V3.375c0-1.036.84-1.875 1.875-1.875ZM9.75 17.25a.75.75 0 0 0-1.5 0V18a.75.75 0 0 0 1.5 0v-.75Zm2.25-3a.75.75 0 0 1 .75.75v3a.75.75 0 0 1-1.5 0v-3a.75.75 0 0 1 .75-.75Zm3.75-1.5a.75.75 0 0 0-1.5 0V18a.75.75 0 0 0 1.5 0v-5.25Z" clip-rule="evenodd" />
                <path d="M14.25 5.25a5.23 5.23 0 0 0-1.279-3.434 9.768 9.768 0 0 1 6.963 6.963A5.23 5.23 0 0 0 16.5 7.5h-1.875a.375.375 0 0 1-.375-.375V5.25Z" />
              </svg>
            </div>
            <!-- Select View -->
            <div (click)="openViewModel()" class="cursor-pointer text-lkq-electric-blue hover:text-lkq-gray-800">
              <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="currentColor" class="size-6">
                <path fill-rule="evenodd" d="M6.32 2.577a49.255 49.255 0 0 1 11.36 0c1.497.174 2.57 1.46 2.57 2.93V21a.75.75 0 0 1-1.085.67L12 18.089l-7.165 3.583A.75.75 0 0 1 3.75 21V5.507c0-1.47 1.073-2.756 2.57-2.93Z" clip-rule="evenodd" />
              </svg>
            </div>
          </div>
        </div>
        <div class="grid grid-cols-1 md:flex md:space-x-2">
          <div>
            <div class="font-medium pl-0.5">{{ t('filters.region') }}</div>
            <nz-select
              class="w-full md:w-28 ng-zorro-disable-typing"
              nzMode="tags"
              [nzDropdownMatchSelectWidth]="false"
              [nzAllowClear]="true"
              [nzShowSearch]="false"
              [nzMaxTagCount]="0"
              [nzMaxTagPlaceholder]="tagPlaceHolder"
              [nzPlaceHolder]="t('filters.placeholder')"
              [(ngModel)]="regions"
              (ngModelChange)="filterChanged()"
            >
              @for(region of dashboardState.dashboardMetadata?.regions ?? []; track region) {
                <nz-option [nzLabel]="region" [nzValue]="region"></nz-option>
              }
            </nz-select>
          </div>
          <div>
            <div class="font-medium pl-0.5">{{ t('filters.business_unit') }}</div>
            <nz-select
              class="w-full md:w-28 disable-typing"
              nzMode="tags"
              [nzDropdownMatchSelectWidth]="false"
              [nzAllowClear]="true"
              [nzShowSearch]="false"
              [nzMaxTagCount]="0"
              [nzMaxTagPlaceholder]="tagPlaceHolder"
              [nzPlaceHolder]="t('filters.placeholder')"
              [(ngModel)]="businessUnits"
              (ngModelChange)="filterChanged()"
            >
              @for(business_unit of dashboardState.dashboardMetadata?.business_units ?? []; track business_unit) {
                <nz-option [nzLabel]="business_unit" [nzValue]="business_unit"></nz-option>
              }
            </nz-select>
          </div>
          <div class="hidden"><!-- TODO: Unhide -->
            <div class="font-medium pl-0.5">{{ t('filters.channel') }}</div>
            <nz-select
              class="w-full md:w-28 disable-typing"
              nzMode="tags"
              [nzDropdownMatchSelectWidth]="false"
              [nzAllowClear]="true"
              [nzShowSearch]="false"
              [nzMaxTagCount]="0"
              [nzMaxTagPlaceholder]="tagPlaceHolder"
              [nzPlaceHolder]="t('filters.placeholder')"
              [(ngModel)]="channels"
              (ngModelChange)="filterChanged()"
            >
              @for(channel of dashboardState.dashboardMetadata?.channels ?? []; track channel) {
                <nz-option [nzLabel]="channel" [nzValue]="channel"></nz-option>
              }
            </nz-select>
          </div>
          <div>
            <div class="font-medium pl-0.5">{{ t('filters.date') }}</div>
            <nz-range-picker class="w-60" [(ngModel)]="dateFilter" (ngModelChange)="filterChanged()" [nzRanges]="ranges"></nz-range-picker>
          </div>
          <div>
            <div class="h-[20px]"></div>
            @if(authService.isAdmin() || authService.isViewManager()) {
              <div (click)="openAddViewModal()" class="h-[32px] flex items-center text-lkq-electric-blue hover:text-lkq-gray-800 cursor-pointer">
                <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="currentColor" class="size-6">
                  <path fill-rule="evenodd" d="M12 2.25c-5.385 0-9.75 4.365-9.75 9.75s4.365 9.75 9.75 9.75 9.75-4.365 9.75-9.75S17.385 2.25 12 2.25ZM12.75 9a.75.75 0 0 0-1.5 0v2.25H9a.75.75 0 0 0 0 1.5h2.25V15a.75.75 0 0 0 1.5 0v-2.25H15a.75.75 0 0 0 0-1.5h-2.25V9Z" clip-rule="evenodd" />
                </svg>
              </div>
            }
          </div>
          <ng-template #tagPlaceHolder let-selectedList>{{  t('filters.value', { count: selectedList.length}) }}</ng-template>
        </div>
      </div>
    </header>
    <main>
      <div class="mx-auto max-w-7xl px-4 py-8 sm:px-6 lg:px-8 space-y-12">
        @for(section of dashboardState.dashboard.sections; track section) {
          <div>
            <div class="flex items-center gap-x-4">
              @if(section.icon) {
                <img [src]="mapIcons(section.icon)" class="w-8 h-8" />
              }
              <h3 class="text-xl font-medium leading-6 text-lkq-gray-900">{{ section.title }}</h3>
            </div>

            <dl class="mt-5 grid grid-cols-1 gap-5 sm:grid-cols-2 lg:grid-cols-3">
              @for(widget of section.widgets; track widget) {
                @defer (on viewport) {
                  <dashboard-statistics-widget [widget]="widget" [section]="section"></dashboard-statistics-widget>
                } @placeholder {
                  <div class="relative overflow-hidden rounded-lg bg-lkq-pure-white shadow">
                    <div class="px-4 py-5 sm:px-6 sm:py-6 h-80">
                        <div class="w-full h-full flex items-center justify-center">
                          <component-loader></component-loader>
                        </div>
                    </div>
                  </div>
                }
              }
            </dl>
          </div>
        }
      </div>
    </main>
  } @else {
    <main>
      <div class="mx-auto max-w-7xl px-4 py-8 sm:px-6 lg:px-8 space-y-6">
        <div class="text-center">
          {{ t('no_dashboards') }}
        </div>
      </div>
    </main>
  }
</ng-container>
