import {
  ChangeDetectionStrategy,
  ChangeDetectorRef,
  Component,
  inject,
  Input,
  OnInit,
} from '@angular/core';
import { Widget } from '@api/interfaces/dashboard/widget.interface';
import { WidgetService } from '@api/services/dashboard/widget.service';
import { lastValueFrom } from 'rxjs';
import { WidgetAnalytics } from '@api/interfaces/dashboard/widget-analytics.interface';
import { NgApexchartsModule } from 'ng-apexcharts';
import { CommonModule } from '@angular/common';
import { ColumnGraphComponent } from '../graphs/column-graph/column-graph.component';
import { LoaderComponent } from '@components/loader/loader.component';
import { DashboardState } from '../dashboard.state';
import { WidgetDataType } from '@api/enums/dashboard/widget-data-type.enum';
import { WidgetType } from '@api/enums/dashboard/widget-type.enum';
import { WidgetPostAnalytics } from '@api/interfaces/dashboard/widget-post-analytics.interface';
import { ImageWidgetComponent } from '../graphs/image-widget/image-widget.component';
import { DashboardDetailModalComponent } from '../modals/dashboard-detail-modal/dashboard-detail-modal.component';
import { ModalService } from '@components/modal/modal.service';
import { SetTargetForWidgetModalComponent } from '../modals/set-target-for-widget-modal/set-target-for-widget-modal.component';
import { Section } from '@api/interfaces/dashboard/section.interface';
import { WidgetEventAnalytics } from '@api/interfaces/dashboard/widget-event-analytics.interface';
import { PieGraphComponent } from '../graphs/pie-graph/pie-graph.component';

@Component({
  selector: 'dashboard-statistics-widget',
  templateUrl: './widget.component.html',
  standalone: true,
  imports: [
    CommonModule,
    NgApexchartsModule,
    ColumnGraphComponent,
    LoaderComponent,
    ImageWidgetComponent,
    PieGraphComponent,
  ],
  changeDetection: ChangeDetectionStrategy.OnPush,
})
export class WidgetComponent implements OnInit {
  // Inputs
  @Input({ required: true }) public widget!: Widget;
  @Input({ required: true }) public section!: Section;

  // Injects
  public dashboardState = inject(DashboardState);
  private widgetService = inject(WidgetService);
  private changeDetectorRef = inject(ChangeDetectorRef);
  private modalService = inject(ModalService);

  // State
  public isLoading = false;
  public widgetAnalytics?: WidgetAnalytics;
  public widgetPostAnalytics?: WidgetPostAnalytics;
  public widgetEventAnalytics?: WidgetEventAnalytics;

  // Enums
  protected readonly widgetDataType = WidgetDataType;

  // Definitions
  public static readonly COLUMN_CHART_TYPES: WidgetType[] = [
    WidgetType.GOOGLE_ANALYTICS_SESSION,
    WidgetType.GOOGLE_ANALYTICS_BOOKINGS,
    WidgetType.MAILCHIMP_SENDS,
    WidgetType.MAILCHIMP_OPEN_RATE,
    WidgetType.MAILCHIMP_CLICK_THROUGH_RATE,
    WidgetType.GOOGLE_ADS_SEARCH_CLICKS,
    WidgetType.GOOGLE_ADS_SEARCH_CLICK_THROUGH_RATE,
    WidgetType.GOOGLE_ADS_SEARCH_COST_PER_CLICK,
    WidgetType.GOOGLE_ADS_SEARCH_IMPRESSIONS,
    WidgetType.GOOGLE_ADS_DISPLAY_CLICKS,
    WidgetType.GOOGLE_ADS_DISPLAY_CLICK_THROUGH_RATE,
    WidgetType.GOOGLE_ADS_DISPLAY_COST_PER_CLICK,
    WidgetType.GOOGLE_ADS_DISPLAY_IMPRESSIONS,
    WidgetType.GOOGLE_ADS_YOUTUBE_VIDEO_VIEWS,
    WidgetType.GOOGLE_ADS_YOUTUBE_COST_PER_MILE,
    WidgetType.GOOGLE_ADS_YOUTUBE_CLICKS,
    WidgetType.GOOGLE_ADS_YOUTUBE_COST_PER_CLICK,
    WidgetType.GOOGLE_ADS_YOUTUBE_CLICK_THROUGH_RATE,
    WidgetType.META_ADS_FACEBOOK_REACH,
    WidgetType.META_ADS_FACEBOOK_SPEND,
    WidgetType.META_ADS_FACEBOOK_CLICK_THROUGH_RATE,
    WidgetType.META_ADS_FACEBOOK_COST_PER_MILE,
    WidgetType.META_ADS_INSTAGRAM_REACH,
    WidgetType.META_ADS_INSTAGRAM_SPEND,
    WidgetType.META_ADS_INSTAGRAM_CLICK_THROUGH_RATE,
    WidgetType.META_ADS_INSTAGRAM_COST_PER_MILE,
    WidgetType.FACEBOOK_REACH,
    WidgetType.FACEBOOK_ENGAGEMENT,
    WidgetType.FACEBOOK_ENGAGEMENT_RATE,
    WidgetType.FACEBOOK_FOLLOWERS,
    WidgetType.INSTAGRAM_REACH,
    WidgetType.INSTAGRAM_ENGAGEMENT,
    WidgetType.INSTAGRAM_ENGAGEMENT_RATE,
    WidgetType.INSTAGRAM_FOLLOWERS,
    WidgetType.YOUTUBE_VIEWS,
    WidgetType.YOUTUBE_ENGAGEMENT,
    WidgetType.YOUTUBE_ENGAGEMENT_RATE,
    WidgetType.YOUTUBE_NUMBER_OF_VIDEOS,
    WidgetType.YOUTUBE_NUMBER_OF_SUBSCRIBERS,
    WidgetType.LINKED_IN_ADS_REACH,
    WidgetType.LINKED_IN_ADS_SPEND,
    WidgetType.LINKED_IN_ADS_CLICK_THROUGH_RATE,
    WidgetType.LINKED_IN_ADS_COST_PER_MILE,
    WidgetType.LINKED_IN_ADS_CLICKS,
    WidgetType.LINKED_IN_COMMUNITY_PROFILE_VIEWS,
    WidgetType.LINKED_IN_COMMUNITY_FOLLOWERS,
    WidgetType.LINKED_IN_COMMUNITY_ENGAGEMENT,
    WidgetType.LINKED_IN_COMMUNITY_ENGAGEMENT_RATE,
    WidgetType.LINKED_IN_COMMUNITY_REACH,
    WidgetType.LINKED_IN_COMMUNITY_POST_REACH,
    WidgetType.LINKED_IN_COMMUNITY_POST_ENGAGEMENT,
    WidgetType.LINKED_IN_COMMUNITY_POST_ENGAGEMENT_RATE,
    WidgetType.LOCALIUM_VIEWS_DIRECTIONS,
    WidgetType.LOCALIUM_VIEWS_GOOGLE,
    WidgetType.LOCALIUM_VIEWS_MAPS,
    WidgetType.LOCALIUM_PHONE_CALLS,
    WidgetType.TIKTOK_VIDEO_COUNT,
    WidgetType.TIKTOK_FOLLOWER_COUNT,
    WidgetType.TIKTOK_VIDEO_VIEWS,
    WidgetType.TIKTOK_VIDEO_ENGAGEMENT,
    WidgetType.TIKTOK_VIDEO_ENGAGEMENT_RATE,
  ];
  public static readonly POST_TYPES: WidgetType[] = [
    WidgetType.INSTAGRAM_TOP_POST,
    WidgetType.INSTAGRAM_BOTTOM_POST,
    WidgetType.YOUTUBE_TOP_VIDEO,
    WidgetType.YOUTUBE_BOTTOM_VIDEO,
    WidgetType.TIKTOK_TOP_VIDEO,
    WidgetType.TIKTOK_BOTTOM_VIDEO,
    WidgetType.META_ADS_FACEBOOK_BOTTOM_CAMPAIGN,
    WidgetType.META_ADS_FACEBOOK_TOP_CAMPAIGN,
    WidgetType.META_ADS_INSTAGRAM_BOTTOM_CAMPAIGN,
    WidgetType.META_ADS_INSTAGRAM_TOP_CAMPAIGN,
    WidgetType.LINKED_IN_ADS_TOP_CAMPAIGN,
    WidgetType.LINKED_IN_ADS_BOTTOM_CAMPAIGN,
    WidgetType.LINKED_IN_COMMUNITY_TOP_POST,
    WidgetType.LINKED_IN_COMMUNITY_BOTTOM_POST,
    WidgetType.FACEBOOK_TOP_POST,
    WidgetType.FACEBOOK_BOTTOM_POST,
  ];
  public static readonly PIE_CHART_TYPES: WidgetType[] = [
    WidgetType.GOOGLE_ANALYTICS_CHANNEL_GROUPS,
  ];

  public ngOnInit() {
    this.loadColumnChartWidgetTypes();
  }

  private async loadColumnChartWidgetTypes(): Promise<void> {
    this.isLoading = true;

    const response = await lastValueFrom(
      this.widgetService.statistics(
        this.widget.id,
        this.dashboardState.filterState,
      ),
    );

    this.isLoading = false;

    if (WidgetComponent.COLUMN_CHART_TYPES.includes(this.widget.type)) {
      this.widgetAnalytics = response.data as WidgetAnalytics;
    }
    if (WidgetComponent.POST_TYPES.includes(this.widget.type)) {
      this.widgetPostAnalytics = response.data as WidgetPostAnalytics;
    }
    if (WidgetComponent.PIE_CHART_TYPES.includes(this.widget.type)) {
      this.widgetEventAnalytics = response.data as WidgetEventAnalytics;
    }

    this.changeDetectorRef.detectChanges();
  }

  public openDetailModel(widget: Widget) {
    this.modalService.open(DashboardDetailModalComponent, {
      widget,
      section: this.section,
    });
  }

  public openTargetModel(widget: Widget) {
    this.modalService.open(SetTargetForWidgetModalComponent, {
      widget,
    });
  }
}
