<div class="relative overflow-hidden rounded-lg bg-lkq-pure-white shadow">
  <div class="px-4 py-5 sm:px-6 sm:py-6 h-80">
    @if (! isLoading && widget.data_type === widgetDataType.COLUMN_CHART && widgetAnalytics) {
      <dashboard-statistics-column-graph
        [title]="widget.title"
        [color]="widget.color"
        [inverseComparisonIndicator]="widget.inverse_comparison_indicator"
        [widgetAnalytics]="widgetAnalytics"
        [filterState]="dashboardState.filterState"
        [target]="widget.target"
        (detailViewClicked)="openDetailModel(widget)"
        (targetViewClicked)="openTargetModel(widget)"
        dataType="number"
      ></dashboard-statistics-column-graph>
    }
    @if (! isLoading && widget.data_type === widgetDataType.COLUMN_CHART_PERCENTAGE && widgetAnalytics) {
      <dashboard-statistics-column-graph
        [title]="widget.title"
        [color]="widget.color"
        [inverseComparisonIndicator]="widget.inverse_comparison_indicator"
        [widgetAnalytics]="widgetAnalytics"
        [filterState]="dashboardState.filterState"
        [target]="widget.target"
        (detailViewClicked)="openDetailModel(widget)"
        (targetViewClicked)="openTargetModel(widget)"
        dataType="percentage"
      ></dashboard-statistics-column-graph>
    }
    @if (! isLoading && widget.data_type === widgetDataType.COLUMN_CHART_EURO && widgetAnalytics) {
      <dashboard-statistics-column-graph
        [title]="widget.title"
        [color]="widget.color"
        [inverseComparisonIndicator]="widget.inverse_comparison_indicator"
        [widgetAnalytics]="widgetAnalytics"
        [filterState]="dashboardState.filterState"
        [target]="widget.target"
        (detailViewClicked)="openDetailModel(widget)"
        (targetViewClicked)="openTargetModel(widget)"
        dataType="euro"
      ></dashboard-statistics-column-graph>
    }
    @if (! isLoading && widget.data_type === widgetDataType.SINGLE_POST && widgetPostAnalytics) {
      <dashboard-statistics-image-widget
        [title]="widget.title"
        [color]="widget.color"
        [inverseComparisonIndicator]="widget.inverse_comparison_indicator"
        [widgetPostAnalytics]="widgetPostAnalytics"
        (detailViewClicked)="openDetailModel(widget)"
      ></dashboard-statistics-image-widget>
    }
    @if (! isLoading && widget.data_type === widgetDataType.PIE_CHART && widgetEventAnalytics) {
      <dashboard-statistics-pie-graph
      [title]="widget.title"
      [inverseComparisonIndicator]="widget.inverse_comparison_indicator"
        [widgetEventAnalytics]="widgetEventAnalytics"
        [filterState]="dashboardState.filterState"
        (detailViewClicked)="openDetailModel(widget)">
      </dashboard-statistics-pie-graph>
    }
    @if (isLoading) {
      <div class="w-full h-full flex items-center justify-center">
        <component-loader></component-loader>
      </div>
    }
  </div>
</div>

