<div
  *transloco="let t; prefix: 'pages.dashboard.modals.dashboard.index'"
  class="relative transform overflow-hidden rounded-lg bg-lkq-pure-white px-4 pb-4 pt-5 text-left shadow-xl transition-all sm:my-8 w-screen sm:max-w-lg sm:p-6"
>
  <div class="absolute right-0 top-0 hidden pr-4 pt-4 sm:block">
    <button (click)="close()" type="button" class="rounded-md bg-lkq-pure-white text-lkq-gray-500 hover:text-lkq-gray-800">
      <span class="sr-only">Close</span>
      <svg class="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor" aria-hidden="true">
        <path stroke-linecap="round" stroke-linejoin="round" d="M6 18L18 6M6 6l12 12" />
      </svg>
    </button>
  </div>
  <div>
    <div class="sm:flex-auto">
      <h1 class="text-base font-medium leading-6 text-lkq-gray-900">{{ t('title') }}</h1>
      <p class="mt-2 text-sm text-lkq-gray-800">{{ t('description') }}</p>
    </div>

    @if(isLoading) {
      <div class="h-10 my-4 flex justify-center">
        <component-loader></component-loader>
      </div>
    } @else {
      <div class="mt-2 flow-root">
        <div class="-my-2 overflow-x-auto">
          <div class="inline-block min-w-full py-2 align-middle max-h-72 overflow-scroll">
            <table class="min-w-full divide-y divide-gray-300">
              <tbody class="divide-y divide-gray-200 bg-lkq-pure-white">
                @if (dashboards.length === 0) {
                  <tr>
                    <td colspan="2" class="whitespace-nowrap pl-2 pr-3 py-4 text-sm text-lkq-gray-500">{{ t('table.no_results') }}</td>
                  </tr>
                }
                @for(dashboard of dashboards; track dashboard.id) {
                  <tr>
                    <td class="whitespace-nowrap pl-2 pr-3 py-4 text-sm text-lkq-gray-800">{{ dashboard.title }}</td>
                    <td class="relative whitespace-nowrap py-4 pl-3 pr-2 text-right text-sm">
                      @if(dashboardState.dashboard?.id === dashboard.id) {
                        <span class="select-none text-lkq-gray-800">{{ t('table.selected') }}</span>
                      } @else {
                        <span (click)="selectDashboard(dashboard)" class="select-none cursor-pointer text-lkq-electric-blue hover:text-lkq-gray-800">{{ t('table.select') }}</span>
                      }
                    </td>
                  </tr>
                }
              </tbody>
            </table>
          </div>
        </div>
      </div>
    }
  </div>
</div>
