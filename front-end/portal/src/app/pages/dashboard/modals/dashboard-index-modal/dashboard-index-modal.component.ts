import {
  ChangeDetectionStrategy,
  ChangeDetectorRef,
  Component,
  inject,
  OnInit,
} from '@angular/core';
import { CommonModule } from '@angular/common';
import { TranslocoDirective } from '@jsverse/transloco';
import { Dashboard } from '@api/interfaces/dashboard/dashboard.interface';
import { LoaderComponent } from '@components/loader/loader.component';
import { ModalService } from '@components/modal/modal.service';
import { lastValueFrom } from 'rxjs';
import { DashboardService } from '@api/services/dashboard/dashboard.service';
import { DashboardState } from '../../dashboard.state';

@Component({
  selector: 'dashboard-statistics-settings-dashboard-index-modal',
  templateUrl: './dashboard-index-modal.component.html',
  standalone: true,
  imports: [CommonModule, TranslocoDirective, LoaderComponent],
  changeDetection: ChangeDetectionStrategy.OnPush,
})
export class DashboardIndexModalComponent implements OnInit {
  // State
  public isLoading = true;
  public dashboards: Dashboard[] = [];

  // Injects
  public dashboardState = inject(DashboardState);
  private modalService = inject(ModalService);
  private dashboardService = inject(DashboardService);
  private changeDetectorRef = inject(ChangeDetectorRef);

  public ngOnInit(): void {
    this.loadDashboards();
  }

  public close(): void {
    this.modalService.close();
  }

  public selectDashboard(dashboard: Dashboard): void {
    this.dashboardState.refresh(dashboard.id);
    this.modalService.close();
  }

  protected async loadDashboards(): Promise<void> {
    this.isLoading = true;

    const response = await lastValueFrom(this.dashboardService.index({}));
    this.dashboards = response.data;

    this.isLoading = false;
    this.changeDetectorRef.detectChanges();
  }
}
