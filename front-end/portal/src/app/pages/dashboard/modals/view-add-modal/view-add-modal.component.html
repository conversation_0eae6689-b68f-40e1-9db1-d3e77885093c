<div
  *transloco="let t; prefix: 'pages.dashboard.modals.view.add'"
  class="relative transform overflow-hidden rounded-lg bg-lkq-pure-white px-4 pb-4 pt-5 text-left shadow-xl transition-all sm:my-8 w-screen sm:max-w-lg sm:p-6"
>
  <div class="absolute right-0 top-0 hidden pr-4 pt-4 sm:block">
    <button (click)="close()" type="button" class="rounded-md bg-lkq-pure-white text-lkq-gray-500 hover:text-lkq-gray-800">
      <span class="sr-only">Close</span>
      <svg class="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor" aria-hidden="true">
        <path stroke-linecap="round" stroke-linejoin="round" d="M6 18L18 6M6 6l12 12" />
      </svg>
    </button>
  </div>
  <div>
    <div class="sm:flex-auto">
      <h1 class="text-base font-medium leading-6 text-lkq-gray-900">{{ t('title') }}</h1>
      <p class="mt-2 text-sm text-lkq-gray-800">{{ t('description') }}</p>
    </div>

    @if(form) {
      @if(isLoading) {
        <div class="h-10 my-4 flex justify-center">
          <component-loader></component-loader>
        </div>
      } @else {
        <div class="mt-4">
          <div>
            <label for="name" class="block text-sm font-medium leading-6 text-lkq-gray-900">{{ t('form.name.label') }}</label>
            <div class="relative mt-2 rounded-md shadow-sm">
              @let nameControl = form.controls.name;
              @let nameControlValid = ! form.controls.name.untouched && form.controls.name.invalid;

              <input
                type="text"
                name="name"
                id="name"
                class="block w-full rounded-md border-0 py-1.5 text-lkq-gray-900 shadow-sm ring-1 ring-inset ring-lkq-gray-200 placeholder:text-lkq-gray-500 focus:ring-2 focus:ring-inset focus:ring-lkq-electric-blue sm:text-sm sm:leading-6"
                [placeholder]="t('form.name.placeholder')"
                [formControl]="nameControl"
                [class.placeholder:text-red-300]="nameControlValid"
                [class.!ring-red-400]="nameControlValid"
              >
              @if (nameControlValid) {
                <div class="pointer-events-none absolute inset-y-0 right-0 flex items-center pr-3">
                  <svg class="h-5 w-5 text-red-500" viewBox="0 0 20 20" fill="currentColor" aria-hidden="true">
                    <path fill-rule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-8-5a.75.75 0 01.75.75v4.5a.75.75 0 01-1.5 0v-4.5A.75.75 0 0110 5zm0 10a1 1 0 100-2 1 1 0 000 2z" clip-rule="evenodd" />
                  </svg>
                </div>
              }
            </div>
          </div>
        </div>

        <div class="mt-5 sm:mt-4 sm:flex sm:flex-row-reverse">
          <button (click)="save()" type="button" class="inline-flex w-full justify-center rounded-md bg-lkq-electric-blue px-3 py-2 text-sm font-medium text-lkq-pure-white shadow-sm hover:bg-lkq-blue-800 sm:ml-3 sm:w-auto">
            {{ t('buttons.save') }}
          </button>
          <button (click)="close()" type="button" class="mt-3 inline-flex w-full justify-center rounded-md bg-lkq-pure-white px-3 py-2 text-sm font-medium text-lkq-gray-900 shadow-sm ring-1 ring-inset ring-lkq-gray-200 hover:bg-lkq-gray-200 sm:mt-0 sm:w-auto">
            {{ t('buttons.cancel') }}
          </button>
        </div>
      }
    }
  </div>
</div>
