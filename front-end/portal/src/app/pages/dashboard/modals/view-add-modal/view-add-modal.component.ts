import {
  ChangeDetectionStrategy,
  Component,
  inject,
  Input,
  OnInit,
} from '@angular/core';
import { CommonModule } from '@angular/common';
import { TranslocoDirective } from '@jsverse/transloco';
import {
  FormControl,
  FormGroup,
  ReactiveFormsModule,
  Validators,
} from '@angular/forms';
import { FilterState } from '@api/interfaces/dashboard/filter-state.interface';
import { ModalService } from '@components/modal/modal.service';
import { DashboardViewService } from '@api/services/dashboard/dashboard-view.service';
import { lastValueFrom } from 'rxjs';
import { Dashboard } from '@api/interfaces/dashboard/dashboard.interface';
import { LoaderComponent } from '@components/loader/loader.component';
import { DashboardState } from '../../dashboard.state';

interface Form {
  channels: FormControl<string[] | null>;
  regions: FormControl<string[] | null>;
  businessUnits: FormControl<string[] | null>;
  startDate: FormControl<Date | null>;
  endDate: FormControl<Date | null>;
  name: FormControl<string | null>;
}

@Component({
  selector: 'dashboard-statistics-settings-view-add-modal',
  templateUrl: './view-add-modal.component.html',
  standalone: true,
  imports: [
    CommonModule,
    TranslocoDirective,
    ReactiveFormsModule,
    LoaderComponent,
  ],
  changeDetection: ChangeDetectionStrategy.OnPush,
})
export class ViewAddModalComponent implements OnInit {
  // Inputs
  @Input() public dashboard!: Dashboard;
  @Input() public filterState?: Partial<FilterState>;

  // State
  public isLoading = false;
  public form?: FormGroup<Form>;

  // Injects
  private modalService = inject(ModalService);
  private dashboardState = inject(DashboardState);
  private dashboardViewService = inject(DashboardViewService);

  public ngOnInit(): void {
    this.initForm();
  }

  public close(): void {
    this.modalService.close();
  }

  public async save(): Promise<void> {
    if (!this.form) {
      return;
    }

    if (!this.form.valid) {
      this.form.markAllAsTouched();
      return;
    }

    this.isLoading = true;

    await lastValueFrom(
      this.dashboardViewService.store(
        this.dashboard.id,
        this.form.value.name as string,
        this.form.value,
      ),
    );

    this.isLoading = false;
    this.dashboardState.refresh();

    this.close();
  }

  protected initForm(): void {
    this.form = new FormGroup<Form>({
      channels: new FormControl(this.filterState?.channels ?? null),
      regions: new FormControl(this.filterState?.regions ?? null),
      businessUnits: new FormControl(this.filterState?.businessUnits ?? null),
      startDate: new FormControl(this.filterState?.startDate ?? null),
      endDate: new FormControl(this.filterState?.endDate ?? null),
      name: new FormControl(null, [Validators.required]),
    });
  }
}
