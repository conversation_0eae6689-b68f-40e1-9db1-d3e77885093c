<div class="relative overflow-hidden rounded-lg bg-lkq-pure-white shadow">
  <div class="px-4 py-5 sm:px-6 sm:py-6 h-96 flex flex-col justify-between space-y-1">
    @if(isLoading) {
      <div class="w-full h-full flex items-center justify-center">
        <component-loader></component-loader>
      </div>
    } @else {
      <h2 class="font-medium text-lg">{{ postAnalytics.channel }}</h2>
      <div *transloco="let t;" class="relative max-h-64 overflow-hidden rounded-lg">
        @if (postAnalytics.url) {
          <img class="object-contain w-full h-full" src="{{ postAnalytics.url }}" alt="Post Image">
        }
        @if (! postAnalytics.url && postAnalytics.text) {
          <div class="text-center text-gray-900">{{ postAnalytics.text }}</div>
        }
        @if (!postAnalytics.text && !postAnalytics.url) {
          <div class="text-center text-gray-500">{{ t('pages.dashboard.no_data') }}</div>
        }
      </div>

      <div class="space-y-1">
        @for (entry of postAnalytics.entries; track entry.name) {
          <div class="flex space-x-2">
            <h2 class="font-medium">{{ entry.name }}:</h2>
            <div class="flex space-x-1">
              <p>{{ entry.valueFormatted }}</p>
              @if(entry.comparison) {
                <p class="text-gray-700 text-s,">({{ entry.comparisonFormatted }})</p>
                <div class="min-h-5">
                  @if(entry.comparisonFormatted) {
                    @let statistics = formattedComparisonPercentage(entry);
                    @if (statistics.type === 'lower') {
                      <div class="inline-flex items-baseline rounded-full bg-red-100 px-1.5 py-0.5 text-xs font-medium text-red-800 md:mt-2 lg:mt-0">
                        <svg class="-ml-1 mr-0.5 h-3 w-3 flex-shrink-0 self-center text-red-500" viewBox="0 0 20 20" fill="currentColor" aria-hidden="true">
                          <path fill-rule="evenodd" d="M10 3a.75.75 0 01.75.75v10.638l3.96-4.158a.75.75 0 111.08 1.04l-5.25 5.5a.75.75 0 01-1.08 0l-5.25-5.5a.75.75 0 111.08-1.04l3.96 4.158V3.75A.75.75 0 0110 3z" clip-rule="evenodd" />
                        </svg>
                        {{ statistics.value }}%
                      </div>
                    }
                    @if(statistics.type === 'higher') {
                      <div class="inline-flex items-baseline rounded-full bg-lkq-sustainability-300 px-1.5 py-0.5 text-xs font-medium text-lkq-sustainability-spring-green md:mt-2 lg:mt-0">
                        <svg class="-ml-1 mr-0.5 h-3 w-3 flex-shrink-0 self-center text-lkq-sustainability-spring-green" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor">
                          <path stroke-linecap="round" stroke-linejoin="round" d="M4.5 10.5 12 3m0 0 7.5 7.5M12 3v18" />
                        </svg>
                        {{ statistics.value }}%
                      </div>
                    }
                    @if(statistics.type === 'unchanged') {
                      <div class="inline-flex items-baseline rounded-full bg-lkq-gray-100 px-1.5 py-0.5 text-xs font-medium text-lkq-gray-800 md:mt-2 lg:mt-0">
                        <svg class="-ml-1 mr-0.5 h-4 w-4 flex-shrink-0 self-center text-lkq-gray-500" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor">
                          <path stroke-linecap="round" stroke-linejoin="round" d="M5 12h14" />
                        </svg>
                        {{ statistics.value }}%
                      </div>
                    }
                  }
                </div>
              }
            </div>
          </div>
        }
      </div>
    }
  </div>
</div>

