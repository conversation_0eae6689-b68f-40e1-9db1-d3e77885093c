<div
  *transloco="let t; prefix: 'pages.dashboard.modals.dashboard.detail'"
  class="relative transform overflow-hidden rounded-lg bg-lkq-gray-100 px-4 pb-4 pt-5 text-left shadow-xl transition-all sm:my-8 w-screen sm:max-w-7xl sm:p-6"
>
  <div class="absolute right-0 top-0 hidden pr-4 pt-4 sm:block">
    <button (click)="close()" type="button" class="rounded-md text-lkq-gray-500 hover:text-lkq-gray-800">
      <span class="sr-only">Close</span>
      <svg class="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor" aria-hidden="true">
        <path stroke-linecap="round" stroke-linejoin="round" d="M6 18L18 6M6 6l12 12" />
      </svg>
    </button>
  </div>
  <div>
    <div class="flex justify-between items-center">
      <div>
        <h1 class="text-lg font-medium leading-6 text-lkq-gray-900">{{ section.title }}</h1>
        <h2 class="text-base font-medium leading-6 text-lkq-gray-900">{{ t('title', { name: widget.title }) }}</h2>
      </div>
      @if(widget.data_type === widgetDataType.SINGLE_POST && needsPostDetailScope) {
        <div class="mt-6" *transloco="let t; prefix: 'enums.dashboard.post-detail'">
          <nz-radio-group [(ngModel)]="selectedPostDetailScope">
            <label nz-radio-button [nzValue]="postDetailScope.TOTAL">{{ t(postDetailScope.TOTAL) }}</label>
            <label nz-radio-button [nzValue]="postDetailScope.SINGLE">{{ t(postDetailScope.SINGLE) }}</label>
          </nz-radio-group>
        </div>
      } @else {
        <div class="mt-6" *transloco="let t; prefix: 'enums.dashboard.widget-detail'">
          <nz-radio-group [(ngModel)]="selectedWidgetDetailScope" (ngModelChange)="loadData()">
            @for(widgetDetailScope of availableWidgetDetailScopes; track widgetDetailScope) {
              <label nz-radio-button [nzValue]="widgetDetailScope">{{ t(widgetDetailScope) }}</label>
            }
          </nz-radio-group>
        </div>
      }
    </div>

    @if(isLoading) {
      <div class="h-10 my-4 flex justify-center">
        <component-loader></component-loader>
      </div>
    } @else {
      <div class="mt-4 max-h-[75vh] overflow-scroll grid grid-cols-3 gap-4 p-1">
        @if (selectedPostDetailScope === postDetailScope.SINGLE) {
          @for(widgetAnalytic of data['post_ids'].values; track widgetAnalytic.value) {
            @defer (on viewport) {
              <dashboard-detail-modal-post [postId]="widgetAnalytic.value" [widget]="widget"></dashboard-detail-modal-post>
            } @placeholder {
              <div class="relative overflow-hidden rounded-lg bg-lkq-pure-white shadow">
                <div class="px-4 py-5 sm:px-6 sm:py-6 h-80">
                    <div class="w-full h-full flex items-center justify-center">
                      <component-loader></component-loader>
                    </div>
                </div>
              </div>
            }
          }
        } @else {
            @for (widgetAnalytic of data | keyvalue; track widgetAnalytic.key) {
              @if(widgetAnalytic.key !== 'post_ids') {
                <div class="relative overflow-hidden rounded-lg bg-lkq-pure-white shadow">
                  <div class="px-4 py-5 sm:px-6 sm:py-6 min-h-80 h-full">
                    @if (widget.data_type === widgetDataType.COLUMN_CHART || widget.data_type === widgetDataType.SINGLE_POST) {
                      <dashboard-statistics-column-graph
                        [color]="widget.color"
                        [inverseComparisonIndicator]="widget.inverse_comparison_indicator"
                        [title]="widgetAnalytic.key"
                        [widgetAnalytics]="widgetAnalytic.value"
                        [filterState]="dashboardState.filterState"
                        [hideButtons]="true"
                        dataType="number"
                      ></dashboard-statistics-column-graph>
                    }
                    @if (widget.data_type === widgetDataType.COLUMN_CHART_PERCENTAGE) {
                      <dashboard-statistics-column-graph
                        [color]="widget.color"
                        [inverseComparisonIndicator]="widget.inverse_comparison_indicator"
                        [title]="widgetAnalytic.key"
                        [widgetAnalytics]="widgetAnalytic.value"
                        [filterState]="dashboardState.filterState"
                        [hideButtons]="true"
                        dataType="percentage"
                      ></dashboard-statistics-column-graph>
                    }
                    @if (widget.data_type === widgetDataType.COLUMN_CHART_EURO) {
                      <dashboard-statistics-column-graph
                        [color]="widget.color"
                        [inverseComparisonIndicator]="widget.inverse_comparison_indicator"
                        [title]="widgetAnalytic.key"
                        [widgetAnalytics]="widgetAnalytic.value"
                        [filterState]="dashboardState.filterState"
                        [hideButtons]="true"
                        dataType="euro"
                      ></dashboard-statistics-column-graph>
                    }
                    @if(widget.data_type === widgetDataType.PIE_CHART) {
                      <dashboard-statistics-pie-graph
                        [title]="widgetAnalytic.key"
                        [inverseComparisonIndicator]="widget.inverse_comparison_indicator"
                        [widgetEventAnalytics]="asWidgetEventAnalytics(widgetAnalytic.value)"
                        [filterState]="dashboardState.filterState"
                        [hideButtons]="true">
                      </dashboard-statistics-pie-graph>
                    }
                  </div>
                </div>
              }
            }
          }
      </div>
    }
  </div>
</div>
