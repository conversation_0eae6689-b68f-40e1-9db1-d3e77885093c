import {
  ChangeDetectorRef,
  Component,
  inject,
  Input,
  OnInit,
} from '@angular/core';
import {
  PostAnalytics,
  PostAnalyticsEntry,
} from '@api/interfaces/dashboard/post-analytics.interface';
import { Widget } from '@api/interfaces/dashboard/widget.interface';
import { PostAnalyticsService } from '@api/services/dashboard/post-analytics.service';
import { lastValueFrom } from 'rxjs';
import { DashboardState } from '../../../dashboard.state';
import { LoaderComponent } from '../../../../../components/loader/loader.component';
import { TranslocoDirective } from '@jsverse/transloco';

@Component({
  selector: 'dashboard-detail-modal-post',
  standalone: true,
  imports: [LoaderComponent, TranslocoDirective],
  templateUrl: './dashboard-detail-modal-post.component.html',
})
export class DashboardDetailModalPostComponent implements OnInit {
  @Input({ required: true }) postId!: number;
  @Input({ required: true }) widget!: Widget;

  public isLoading: boolean = false;

  protected postAnalytics!: PostAnalytics;

  private postAnalyticsService = inject(PostAnalyticsService);
  private changeDetectorRef = inject(ChangeDetectorRef);
  public dashboardState = inject(DashboardState);

  public ngOnInit(): void {
    this.loadStatistics();
  }

  public formattedComparisonPercentage(
    postAnalyticsEntry: PostAnalyticsEntry,
  ): {
    value: number;
    type: 'higher' | 'lower' | 'unchanged';
  } {
    const percentage =
      ((postAnalyticsEntry.value - (postAnalyticsEntry.comparison ?? 0)) /
        (postAnalyticsEntry.comparison ?? 0)) *
      100;

    let type: 'higher' | 'lower' | 'unchanged' = 'unchanged';
    if (percentage < 0) {
      type = 'lower';
    }
    if (percentage > 0) {
      type = 'higher';
    }

    return {
      value: Math.round(Math.abs(percentage) * 10) / 10,
      type,
    };
  }

  private async loadStatistics(): Promise<void> {
    this.isLoading = true;

    const response = await lastValueFrom(
      this.postAnalyticsService.statistics(
        this.widget.id,
        this.postId,
        this.dashboardState.filterState,
      ),
    );

    this.postAnalytics = response.data;

    this.isLoading = false;
    this.changeDetectorRef.detectChanges();
  }
}
