import {
  ChangeDetectionStrategy,
  ChangeDetectorRef,
  Component,
  inject,
  Input,
  OnInit,
} from '@angular/core';
import { CommonModule } from '@angular/common';
import { TranslocoDirective } from '@jsverse/transloco';
import { LoaderComponent } from '@components/loader/loader.component';
import { ModalService } from '@components/modal/modal.service';
import { lastValueFrom } from 'rxjs';
import { Widget } from '@api/interfaces/dashboard/widget.interface';
import { WidgetDetailService } from '@api/services/dashboard/widget-detail.service';
import { WidgetDetailScope } from '@api/enums/dashboard/widget-detail-scope.enum';
import { DashboardState } from '../../dashboard.state';
import { WidgetAnalytics } from '@api/interfaces/dashboard/widget-analytics.interface';
import { ColumnGraphComponent } from '../../graphs/column-graph/column-graph.component';
import { NzRadioComponent, NzRadioGroupComponent } from 'ng-zorro-antd/radio';
import { FormsModule } from '@angular/forms';
import { WidgetDataType } from '@api/enums/dashboard/widget-data-type.enum';
import { WidgetComponent } from '../../widget/widget.component';
import { Section } from '@api/interfaces/dashboard/section.interface';
import { WidgetEventAnalytics } from '@api/interfaces/dashboard/widget-event-analytics.interface';
import { PieGraphComponent } from '../../graphs/pie-graph/pie-graph.component';
import { WidgetType } from '@api/enums/dashboard/widget-type.enum';
import { DashboardDetailModalPostComponent } from './dashboard-detail-modal-post/dashboard-detail-modal-post.component';

enum PostDetailScope {
  TOTAL = 'TOTAL',
  SINGLE = 'SINGLE',
}

@Component({
  selector: 'dashboard-detail-modal',
  templateUrl: './dashboard-detail-modal.component.html',
  standalone: true,
  imports: [
    CommonModule,
    TranslocoDirective,
    LoaderComponent,
    ColumnGraphComponent,
    NzRadioComponent,
    NzRadioGroupComponent,
    FormsModule,
    PieGraphComponent,
    DashboardDetailModalPostComponent,
  ],
  changeDetection: ChangeDetectionStrategy.OnPush,
})
export class DashboardDetailModalComponent implements OnInit {
  // Input
  @Input() public widget!: Widget;
  @Input() public section!: Section;

  // State
  public isLoading = true;
  public selectedWidgetDetailScope = WidgetDetailScope.DATASOURCE;
  public availableWidgetDetailScopes: WidgetDetailScope[] = [];
  public selectedPostDetailScope = PostDetailScope.TOTAL;
  public data: { [label: string]: WidgetAnalytics | WidgetEventAnalytics } = {};
  public needsPostDetailScope: boolean = false;

  // Injects
  public dashboardState = inject(DashboardState);
  private modalService = inject(ModalService);
  private widgetDetailService = inject(WidgetDetailService);
  private changeDetectorRef = inject(ChangeDetectorRef);

  // Enums
  protected readonly widgetDataType = WidgetDataType;
  protected readonly postDetailScope = PostDetailScope;

  private static readonly NeedPostDetailScopeWidgets: WidgetType[] = [
    WidgetType.FACEBOOK_BOTTOM_POST,
    WidgetType.FACEBOOK_TOP_POST,
    WidgetType.INSTAGRAM_BOTTOM_POST,
    WidgetType.INSTAGRAM_TOP_POST,
    WidgetType.YOUTUBE_BOTTOM_VIDEO,
    WidgetType.YOUTUBE_TOP_VIDEO,
    WidgetType.LINKED_IN_COMMUNITY_TOP_POST,
    WidgetType.LINKED_IN_COMMUNITY_BOTTOM_POST,
  ];

  public ngOnInit(): void {
    if (
      WidgetComponent.COLUMN_CHART_TYPES.includes(this.widget.type) ||
      WidgetComponent.PIE_CHART_TYPES.includes(this.widget.type)
    ) {
      this.setAvailableWidgetDetailScopes();
    }

    this.needsPostDetailScope =
      DashboardDetailModalComponent.NeedPostDetailScopeWidgets.includes(
        this.widget.type,
      );

    this.loadData();
  }

  public close(): void {
    this.modalService.close();
  }

  public asWidgetEventAnalytics(
    analytics: WidgetAnalytics | WidgetEventAnalytics,
  ): WidgetEventAnalytics {
    return analytics as WidgetEventAnalytics;
  }

  protected setAvailableWidgetDetailScopes(): void {
    const dashboardMetadata = this.dashboardState.dashboardMetadata;
    const availableSelectors = [WidgetDetailScope.DATASOURCE];

    if (
      dashboardMetadata?.business_units &&
      dashboardMetadata?.business_units.length > 1
    ) {
      availableSelectors.push(WidgetDetailScope.BUSINESS_UNIT);
    }

    if (dashboardMetadata?.regions && dashboardMetadata?.regions.length > 1) {
      availableSelectors.push(WidgetDetailScope.REGION);
    }

    this.availableWidgetDetailScopes = availableSelectors;
  }

  protected async loadData(): Promise<void> {
    this.isLoading = true;

    const response = await lastValueFrom(
      this.widgetDetailService.statistics(
        this.widget.id,
        this.selectedWidgetDetailScope,
        this.dashboardState.filterState,
      ),
    );

    this.data = response.data;

    this.isLoading = false;
    this.changeDetectorRef.detectChanges();
  }
}
