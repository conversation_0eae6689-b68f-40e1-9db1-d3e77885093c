import {
  ChangeDetectionStrategy,
  Component,
  inject,
  Input,
  OnInit,
} from '@angular/core';
import { CommonModule } from '@angular/common';
import { TranslocoDirective } from '@jsverse/transloco';
import {
  FormControl,
  FormGroup,
  ReactiveFormsModule,
  Validators,
} from '@angular/forms';
import { ModalService } from '@components/modal/modal.service';
import { LoaderComponent } from '@components/loader/loader.component';
import { DashboardState } from '../../dashboard.state';
import { Widget } from '@api/interfaces/dashboard/widget.interface';
import { WidgetDataType } from '@api/enums/dashboard/widget-data-type.enum';
import { NgxMaskDirective, provideNgxMask } from 'ngx-mask';
import { WidgetTargetService } from '@api/services/dashboard/widget-target.service';
import { lastValueFrom } from 'rxjs';

interface Form {
  target: FormControl<number | null>;
}

@Component({
  selector: 'dashboard-statistics-settings-set-target-for-widget-modal',
  templateUrl: './set-target-for-widget-modal.component.html',
  standalone: true,
  imports: [
    CommonModule,
    TranslocoDirective,
    ReactiveFormsModule,
    LoaderComponent,
    NgxMaskDirective,
  ],
  providers: [provideNgxMask()],
  changeDetection: ChangeDetectionStrategy.OnPush,
})
export class SetTargetForWidgetModalComponent implements OnInit {
  // Inputs
  @Input() public widget!: Widget;

  // State
  public isLoading = false;
  public form?: FormGroup<Form>;
  public widgetDataType = WidgetDataType;

  // Injects
  private modalService = inject(ModalService);
  private dashboardState = inject(DashboardState);
  private widgetTargetService = inject(WidgetTargetService);

  public ngOnInit(): void {
    this.initForm();
  }

  public close(): void {
    this.modalService.close();
  }

  public async save(): Promise<void> {
    if (!this.form) {
      return;
    }

    if (!this.form.valid) {
      this.form.markAllAsTouched();
      return;
    }

    this.isLoading = true;

    await lastValueFrom(
      this.widgetTargetService.update(
        this.widget.id,
        this.form.value.target as number,
      ),
    );

    this.isLoading = false;
    this.dashboardState.refresh();

    this.close();
  }

  public async deleteTarget(): Promise<void> {
    this.isLoading = true;

    await lastValueFrom(this.widgetTargetService.destroy(this.widget.id));

    this.isLoading = false;
    this.dashboardState.refresh();

    this.close();
  }

  protected initForm(): void {
    this.form = new FormGroup<Form>({
      target: new FormControl(this.widget.target || null, [
        Validators.required,
      ]),
    });
  }

  public getPlaceholder(): string {
    switch (this.widget.data_type) {
      case WidgetDataType.COLUMN_CHART:
        return '100';
      case WidgetDataType.COLUMN_CHART_PERCENTAGE:
        return '75%';
      case WidgetDataType.COLUMN_CHART_EURO:
        return '€ 1.000';
      default:
        return '';
    }
  }

  public getMask(): string {
    switch (this.widget.data_type) {
      case WidgetDataType.COLUMN_CHART:
        return '0*';
      case WidgetDataType.COLUMN_CHART_PERCENTAGE:
        return 'percent';
      case WidgetDataType.COLUMN_CHART_EURO:
        return 'separator.2';
      default:
        return '';
    }
  }

  public getPrefix(): string {
    return this.widget.data_type === WidgetDataType.COLUMN_CHART_EURO
      ? '€ '
      : '';
  }

  public getSuffix(): string {
    return this.widget.data_type === WidgetDataType.COLUMN_CHART_PERCENTAGE
      ? ' %'
      : '';
  }
}
