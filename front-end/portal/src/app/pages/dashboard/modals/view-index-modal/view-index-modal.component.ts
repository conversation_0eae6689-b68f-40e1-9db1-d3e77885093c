import {
  ChangeDetectionStrategy,
  Component,
  inject,
  Input,
} from '@angular/core';
import { CommonModule } from '@angular/common';
import { TranslocoDirective } from '@jsverse/transloco';
import { LoaderComponent } from '@components/loader/loader.component';
import { DashboardView } from '@api/interfaces/dashboard/dashboard-view.interface';
import { DashboardViewService } from '@api/services/dashboard/dashboard-view.service';
import { lastValueFrom } from 'rxjs';
import { DashboardState } from '../../dashboard.state';
import { ConfirmationModalService } from '@components/confirmation-modal/confirmation-modal.service';
import { ModalService } from '@components/modal/modal.service';

@Component({
  selector: 'dashboard-statistics-settings-view-index-modal',
  templateUrl: './view-index-modal.component.html',
  standalone: true,
  imports: [CommonModule, TranslocoDirective, LoaderComponent],
  changeDetection: ChangeDetectionStrategy.OnPush,
})
export class ViewIndexModalComponent {
  // Inputs
  @Input() views?: DashboardView[];

  // Injects
  private dashboardState = inject(DashboardState);
  private dashboardViewService = inject(DashboardViewService);
  private confirmationModalService = inject(ConfirmationModalService);
  private modalService = inject(ModalService);

  public async select(view: DashboardView): Promise<void> {
    const startDate = view.start_date ? new Date(view.start_date) : null;
    const endDate = view.end_date ? new Date(view.end_date) : null;

    this.dashboardState.updateFilters({
      channels: view.channels,
      regions: view.regions,
      businessUnits: view.business_units,
      startDate,
      endDate,
    });

    this.dashboardState.refresh(undefined, view);
    this.close();
  }

  public async delete(view: DashboardView): Promise<void> {
    await this.confirmationModalService.confirm(
      {
        confirmed: async () => {
          await lastValueFrom(
            this.dashboardViewService.destroy(view.dashboard_id, view.id),
          );
          await this.dashboardState.refresh();
        },
      },
      {
        title: 'pages.dashboard.modals.view.index.delete.title',
        description: 'pages.dashboard.modals.view.index.delete.description',
      },
    );
  }

  public close(): void {
    this.modalService.close();
  }
}
