import {
  ChangeDetectionStrategy,
  ChangeDetectorRef,
  Component,
  DestroyRef,
  inject,
  OnInit,
} from '@angular/core';
import { TranslocoDirective, TranslocoService } from '@jsverse/transloco';
import { DashboardState } from './dashboard.state';
import { CommonModule } from '@angular/common';
import { WidgetComponent } from './widget/widget.component';
import {
  NzDatePickerComponent,
  NzRangePickerComponent,
} from 'ng-zorro-antd/date-picker';
import {
  FormControl,
  FormGroup,
  FormsModule,
  ReactiveFormsModule,
} from '@angular/forms';
import { takeUntilDestroyed } from '@angular/core/rxjs-interop';
import { NzOptionComponent, NzSelectComponent } from 'ng-zorro-antd/select';
import { ModalService } from '@components/modal/modal.service';
import { ViewIndexModalComponent } from './modals/view-index-modal/view-index-modal.component';
import { DashboardIndexModalComponent } from './modals/dashboard-index-modal/dashboard-index-modal.component';
import { ViewAddModalComponent } from './modals/view-add-modal/view-add-modal.component';
import { AuthService } from '@services/auth.service';
import { LoaderComponent } from '@components/loader/loader.component';
import { Icon } from '@api/enums/dashboard/icon.enum';
import {
  endOfMonth,
  startOfWeek,
  endOfWeek,
  subWeeks,
  startOfMonth,
  subMonths,
  startOfQuarter,
  endOfQuarter,
  subQuarters,
  startOfYear,
  endOfYear,
  subYears,
  subDays,
} from 'date-fns';

interface FilterForm {
  dates: FormControl<Date[] | null>;
  channels: FormControl<string[] | null>;
  businessUnits: FormControl<string[] | null>;
  regions: FormControl<string[] | null>;
}

@Component({
  selector: 'pages-dashboard',
  templateUrl: './dashboard.component.html',
  standalone: true,
  imports: [
    TranslocoDirective,
    WidgetComponent,
    CommonModule,
    NzDatePickerComponent,
    NzRangePickerComponent,
    FormsModule,
    ReactiveFormsModule,
    NzSelectComponent,
    NzOptionComponent,
    LoaderComponent,
  ],
  changeDetection: ChangeDetectionStrategy.OnPush,
})
export class DashboardComponent implements OnInit {
  // Injects
  public dashboardState = inject(DashboardState);
  public authService = inject(AuthService);
  private changeDetectorRef = inject(ChangeDetectorRef);
  private destroyRef = inject(DestroyRef);
  private modalService = inject(ModalService);
  private translocoService = inject(TranslocoService);

  // State
  public dateFilter: Date[] | null = null;
  public channels: string[] | null = null;
  public businessUnits: string[] | null = null;
  public regions: string[] | null = null;

  // Form
  public form = new FormGroup<FilterForm>({
    dates: new FormControl(null),
    channels: new FormControl(null),
    businessUnits: new FormControl(null),
    regions: new FormControl(null),
  });

  // Datepicker
  public ranges: { [key: string]: Date[] } = {};

  public ngOnInit() {
    this.loadDashboard();
    this.listenToDashboardRefresh();
    this.initForm();
    this.setRanges();
  }

  private async loadDashboard(): Promise<void> {
    const user = this.authService.getUser();
    const metadata = this.authService.getMetadata();
    const sortedDashboards = metadata?.dashboards.sort(
      (a, b) => a.sort - b.sort,
    );
    const firstDashboard = sortedDashboards?.[0];
    const defaultDashboardId = firstDashboard?.id || user?.dashboards[0];

    if (!defaultDashboardId) {
      return;
    }

    await this.dashboardState.load(defaultDashboardId);
    this.setDashboard();
  }

  private listenToDashboardRefresh(): void {
    this.dashboardState.refresh$
      .pipe(takeUntilDestroyed(this.destroyRef))
      .subscribe({
        next: () => this.setDashboard(),
      });
  }

  private setRanges(): void {
    const today = new Date();
    const t = this.translocoService;

    this.ranges = {
      [t.translate('pages.dashboard.date_ranges.this_week')]: [
        startOfWeek(today),
        endOfWeek(today),
      ],
      [t.translate('pages.dashboard.date_ranges.last_week')]: [
        startOfWeek(subWeeks(today, 1)),
        endOfWeek(subWeeks(today, 1)),
      ],
      [t.translate('pages.dashboard.date_ranges.this_month')]: [
        startOfMonth(today),
        endOfMonth(today),
      ],
      [t.translate('pages.dashboard.date_ranges.last_month')]: [
        startOfMonth(subMonths(today, 1)),
        endOfMonth(subMonths(today, 1)),
      ],
      [t.translate('pages.dashboard.date_ranges.this_quarter')]: [
        startOfQuarter(today),
        endOfQuarter(today),
      ],
      [t.translate('pages.dashboard.date_ranges.last_quarter')]: [
        startOfQuarter(subQuarters(today, 1)),
        endOfQuarter(subQuarters(today, 1)),
      ],
      [t.translate('pages.dashboard.date_ranges.this_year')]: [
        startOfYear(today),
        endOfYear(today),
      ],
      [t.translate('pages.dashboard.date_ranges.last_year')]: [
        startOfYear(subYears(today, 1)),
        endOfYear(subYears(today, 1)),
      ],
    };
  }

  private setDashboard(): void {
    const filterState = this.dashboardState.filterState;

    if (filterState?.startDate && filterState?.endDate) {
      this.dateFilter = [filterState?.startDate, filterState?.endDate];
    }

    if (!filterState?.startDate && !filterState?.endDate) {
      this.dateFilter = null;
    }

    this.channels = filterState?.channels ?? null;
    this.regions = filterState?.regions ?? null;
    this.businessUnits = filterState?.businessUnits ?? null;

    this.changeDetectorRef.detectChanges();
  }

  private initForm(): void {
    this.form.valueChanges.pipe(takeUntilDestroyed(this.destroyRef)).subscribe({
      next: (value) => {
        this.dashboardState.updateFilters({
          startDate: value.dates?.[0] ?? null,
          endDate: value.dates?.[1] ?? null,
          channels: this.channels,
          businessUnits: this.businessUnits,
          regions: this.regions,
        });

        this.dashboardState.refresh();
      },
    });
  }

  protected filterChanged(): void {
    if (
      this.dateFilter !== null &&
      (!this.dateFilter[0] || !this.dateFilter[1])
    ) {
      const endDate = subDays(new Date(), 1);
      const startDate = subMonths(endDate, 1);

      this.dateFilter = [startDate, endDate];
    }

    this.form.setValue({
      dates: this.dateFilter,
      channels: this.channels,
      businessUnits: this.businessUnits,
      regions: this.regions,
    });
  }

  public openDashboardPickerModal() {
    this.modalService.open(DashboardIndexModalComponent);
  }

  public openViewModel() {
    this.modalService.open(ViewIndexModalComponent, {
      views: this.dashboardState.dashboardMetadata?.views,
    });
  }

  public openAddViewModal() {
    const dashboard = this.dashboardState.dashboard;

    if (!dashboard) {
      return;
    }

    this.modalService.open(ViewAddModalComponent, {
      dashboard: dashboard,
      filterState: this.dashboardState.filterState,
    });
  }

  public mapIcons(icon: Icon): string {
    const mapping = {
      FACEBOOK: 'facebook.png',
      GOOGLE: 'google.png',
      GOOGLE_ADS: 'google_ads.png',
      LINKEDIN: 'linkedin.png',
      MAILCHIMP: 'mailchimp.png',
      TIKTOK: 'tiktok.png',
      YOUTUBE: 'youtube.png',
      INSTAGRAM: 'instagram.png',
    };

    return 'images/icons/' + mapping[icon];
  }
}
