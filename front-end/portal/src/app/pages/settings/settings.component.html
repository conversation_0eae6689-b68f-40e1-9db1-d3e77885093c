<ng-container *transloco="let t; prefix: 'pages.settings'" >
  <header class="pt-10">
    <div class="mx-auto max-w-7xl px-4 sm:px-6 lg:px-8">
      <h1 class="text-3xl font-medium leading-tight tracking-tight text-lkq-gray-900">
        {{ t('title') }}
      </h1>
    </div>
  </header>
  <main>
    <div class="mx-auto max-w-3xl px-4 sm:px-6 lg:max-w-7xl lg:px-8 pt-4">

      <!-- Menu -->
      <div class="grid grid-cols-1 items-start gap-4 lg:grid-cols-4 lg:gap-8">
        <div class="grid grid-cols-1 gap-4">
          <section aria-labelledby="section-2-title">
            <div class="overflow-hidden rounded-lg bg-lkq-pure-white shadow">
              <div class="p-6">
                <nav class="flex flex-1 flex-col" aria-label="Sidebar">
                  <ul role="list" class="flex flex-1 flex-col gap-y-7">
                    <li>
                      <ul role="list" class="-mx-2 space-y-1">

                        <li>
                          <a routerLink="/settings/users" routerLinkActive="bg-lkq-gray-100" class="block rounded-md py-2 pl-4 pr-2 text-sm font-medium leading-6 text-lkq-gray-800 hover:text-lkq-electric-blue hover:bg-lkq-gray-100">
                            {{ t('menu.users') }}
                          </a>
                        </li>

                        <li>
                          <div>
                            <div type="button" class="flex w-full items-center gap-x-3 rounded-md py-2 px-4 text-left text-sm font-medium leading-6 text-lkq-gray-800">
                              {{ t('menu.connections') }}
                            </div>
                            <ul class="mt-1 px-2">
                              <li>
                                <a routerLink="/settings/connections/google" routerLinkActive="bg-lkq-gray-100" class="block rounded-md py-2 pl-9 pr-2 text-sm leading-6 text-lkq-gray-800 hover:text-lkq-electric-blue hover:bg-lkq-gray-100">{{ t('menu.google') }}</a>
                              </li>
                              <li>
                                <a routerLink="/settings/connections/google-business-profiles" routerLinkActive="bg-lkq-gray-100" class="block rounded-md py-2 pl-9 pr-2 text-sm leading-6 text-lkq-gray-800 hover:text-lkq-electric-blue hover:bg-lkq-gray-100">{{ t('menu.google-business-profiles') }}</a>
                              </li>
                              <li>
                                <a routerLink="/settings/connections/youtube" routerLinkActive="bg-lkq-gray-100" class="block rounded-md py-2 pl-9 pr-2 text-sm leading-6 text-lkq-gray-800 hover:text-lkq-electric-blue hover:bg-lkq-gray-100">{{ t('menu.youtube') }}</a>
                              </li>
                              <li>
                                <a routerLink="/settings/connections/facebook" routerLinkActive="bg-lkq-gray-100" class="block rounded-md py-2 pl-9 pr-2 text-sm leading-6 text-lkq-gray-800 hover:text-lkq-electric-blue hover:bg-lkq-gray-100">{{ t('menu.facebook') }}</a>
                              </li>
                              <li>
                                <a routerLink="/settings/connections/mailchimp" routerLinkActive="bg-lkq-gray-100" class="block rounded-md py-2 pl-9 pr-2 text-sm leading-6 text-lkq-gray-800 hover:text-lkq-electric-blue hover:bg-lkq-gray-100">{{ t('menu.mailchimp') }}</a>
                              </li>
                              <li>
                                <a routerLink="/settings/connections/linkedin-ads" routerLinkActive="bg-lkq-gray-100" class="block rounded-md py-2 pl-9 pr-2 text-sm leading-6 text-lkq-gray-800 hover:text-lkq-electric-blue hover:bg-lkq-gray-100">{{ t('menu.linkedin-ads') }}</a>
                              </li>
                              <li>
                                <a routerLink="/settings/connections/linkedin-community" routerLinkActive="bg-lkq-gray-100" class="block rounded-md py-2 pl-9 pr-2 text-sm leading-6 text-lkq-gray-800 hover:text-lkq-electric-blue hover:bg-lkq-gray-100">{{ t('menu.linkedin-community') }}</a>
                              </li>
                              <li>
                                <a routerLink="/settings/connections/tiktok-organic" routerLinkActive="bg-lkq-gray-100" class="block rounded-md py-2 pl-9 pr-2 text-sm leading-6 text-lkq-gray-800 hover:text-lkq-electric-blue hover:bg-lkq-gray-100">{{ t('menu.tiktok-organic') }}</a>
                              </li>
                              <li>
                                <a routerLink="/settings/connections/tiktok-business" routerLinkActive="bg-lkq-gray-100" class="block rounded-md py-2 pl-9 pr-2 text-sm leading-6 text-lkq-gray-800 hover:text-lkq-electric-blue hover:bg-lkq-gray-100">{{ t('menu.tiktok-business') }}</a>
                              </li>
                            </ul>
                          </div>
                        </li>

                        @if(authService.isSuperAdmin()) {
                          <li>
                            <div>
                              <div type="button" class="flex w-full items-center gap-x-3 rounded-md py-2 px-4 text-left text-sm font-medium leading-6 text-lkq-gray-800">
                                {{ t('menu.data-source') }}
                              </div>
                              <ul class="mt-1 px-2">
                                <li>
                                  <a routerLink="/settings/data-source/business-units" routerLinkActive="bg-lkq-gray-100" class="block rounded-md py-2 pl-9 pr-2 text-sm leading-6 text-lkq-gray-800 hover:text-lkq-electric-blue hover:bg-lkq-gray-100">{{ t('menu.business-units') }}</a>
                                </li>
                                <li>
                                  <a routerLink="/settings/data-source/regions" routerLinkActive="bg-lkq-gray-100" class="block rounded-md py-2 pl-9 pr-2 text-sm leading-6 text-lkq-gray-800 hover:text-lkq-electric-blue hover:bg-lkq-gray-100">{{ t('menu.regions') }}</a>
                                </li>
                                <li>
                                  <a routerLink="/settings/data-source/dashboards" routerLinkActive="bg-lkq-gray-100" class="block rounded-md py-2 pl-9 pr-2 text-sm leading-6 text-lkq-gray-800 hover:text-lkq-electric-blue hover:bg-lkq-gray-100">{{ t('menu.dashboards') }}</a>
                                </li>
                                <li>
                                  <a routerLink="/settings/data-source/sources" routerLinkActive="bg-lkq-gray-100" class="block rounded-md py-2 pl-9 pr-2 text-sm leading-6 text-lkq-gray-800 hover:text-lkq-electric-blue hover:bg-lkq-gray-100">{{ t('menu.sources') }}</a>
                                </li>
                                <li>
                                  <a routerLink="/settings/data-source/onboarding" routerLinkActive="bg-lkq-gray-100" class="block rounded-md py-2 pl-9 pr-2 text-sm leading-6 text-lkq-gray-800 hover:text-lkq-electric-blue hover:bg-lkq-gray-100">{{ t('menu.onboarding') }}</a>
                                </li>
                              </ul>
                            </div>
                          </li>
                        }


                        <li>
                          <a routerLink="/settings/helpdesk" routerLinkActive="bg-lkq-gray-100" class="block rounded-md py-2 pl-4 pr-2 text-sm font-medium leading-6 text-lkq-gray-800 hover:text-lkq-electric-blue hover:bg-lkq-gray-100">{{ t('menu.helpdesk') }}</a>
                        </li>

                      </ul>
                    </li>
                  </ul>
                </nav>
              </div>
            </div>
          </section>
        </div>

        <!-- Content -->
        <div class="grid grid-cols-1 lg:col-span-3">
          <router-outlet></router-outlet>
        </div>

      </div>
    </div>
  </main>
</ng-container>
