import { ChangeDetectionStrategy, Component, inject } from '@angular/core';
import { CommonModule } from '@angular/common';
import {
  FormControl,
  FormGroup,
  ReactiveFormsModule,
  Validators,
} from '@angular/forms';
import { Router } from '@angular/router';
import { lastValueFrom } from 'rxjs';
import { HelpdeskService } from '@api/services/settings/helpdesk.service';
import { LoaderComponent } from '@components/loader/loader.component';
import { ToastService } from '@components/toast/toast.service';
import { TranslocoDirective, TranslocoService } from '@jsverse/transloco';

interface Form {
  subject: FormControl<string>;
  message: FormControl<string>;
}

@Component({
  selector: 'pages-settings-helpdesk',
  templateUrl: './helpdesk.component.html',
  standalone: true,
  imports: [
    CommonModule,
    LoaderComponent,
    ReactiveFormsModule,
    TranslocoDirective,
  ],
  changeDetection: ChangeDetectionStrategy.OnPush,
})
export class HelpdeskComponent {
  // Injects
  private router = inject(Router);
  private helpdeskService = inject(HelpdeskService);
  private toastService = inject(ToastService);
  private translocoService = inject(TranslocoService);

  // State
  public isLoading = false;

  // Form
  public form = new FormGroup<Form>({
    subject: new FormControl('', {
      validators: [Validators.required],
      nonNullable: true,
    }),
    message: new FormControl('', {
      validators: [Validators.required],
      nonNullable: true,
    }),
  });

  public async save(): Promise<void> {
    if (this.isLoading) {
      return;
    }

    if (!this.form.valid) {
      this.form.markAllAsTouched();
      return;
    }

    this.isLoading = true;

    await lastValueFrom(
      this.helpdeskService.store(
        this.form.controls.subject.value,
        this.form.controls.message.value,
      ),
    );

    this.toastService.addToast({
      title: this.translocoService.translate(
        'pages.settings.helpdesk.request.toasts.submitted.title',
      ),
      message: this.translocoService.translate(
        'pages.settings.helpdesk.request.toasts.submitted.description',
      ),
      icon: 'success',
      duration: 5000,
    });

    await this.router.navigateByUrl(`/settings/helpdesk`);
  }
}
