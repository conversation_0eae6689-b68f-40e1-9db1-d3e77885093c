<div *transloco="let t; prefix: 'pages.settings.helpdesk.request'" class="overflow-hidden rounded-lg bg-lkq-pure-white shadow">
  <div class="p-6 relative">

    @if (isLoading) {
      <div class="overlay absolute inset-0 flex items-center justify-center">
        <component-loader></component-loader>
      </div>
    }

    <form>
      <h2 class="text-base font-medium leading-7 text-lkq-gray-900">{{ t('title') }}</h2>
      <p class="mt-1 text-sm leading-6 text-lkq-gray-800">{{ t('description') }}</p>

      <div class="mt-6 grid grid-cols-1 gap-x-6 gap-y-6 sm:grid-cols-6">
        <div class="col-span-full">
          <label for="subject" class="block text-sm font-medium leading-6 text-lkq-gray-900">{{ t('form.subject') }}</label>
          <div class="mt-2">
            <input
              [formControl]="form.controls.subject"
              type="text"
              name="subject"
              id="subject"
              class="block w-full rounded-md border-0 py-1.5 text-lkq-gray-900 shadow-sm ring-1 ring-inset ring-lkq-gray-200 placeholder:text-lkq-gray-500 focus:ring-2 focus:ring-inset focus:ring-lkq-electric-blue sm:text-sm sm:leading-6"
            >
          </div>
        </div>

        <div class="col-span-full">
          <label for="about" class="block text-sm font-medium leading-6 text-lkq-gray-900">{{ t('form.message') }}</label>
          <div class="mt-2">
            <textarea
              [formControl]="form.controls.message"
              id="about"
              name="about"
              rows="6"
              class="block w-full rounded-md border-0 py-1.5 text-lkq-gray-900 shadow-sm ring-1 ring-inset ring-lkq-gray-200 placeholder:text-lkq-gray-500 focus:ring-2 focus:ring-inset focus:ring-lkq-electric-blue sm:text-sm sm:leading-6"
            ></textarea>
          </div>
        </div>

      </div>

      <div class="mt-6 flex items-center justify-end gap-x-6">
        <button (click)="save()" type="button" class="inline-flex items-center rounded-md bg-lkq-electric-blue px-3 py-2 text-sm font-medium text-lkq-pure-white shadow-sm hover:bg-lkq-blue-800 focus-visible:outline focus-visible:outline-2 focus-visible:outline-offset-2 focus-visible:bg-lkq-blue-800">
          {{ t('send') }}
        </button>
      </div>
    </form>


  </div>
</div>
