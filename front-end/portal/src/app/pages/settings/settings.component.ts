import { ChangeDetectionStrategy, Component, inject } from '@angular/core';
import { RouterModule, RouterOutlet } from '@angular/router';
import { TranslocoDirective } from '@jsverse/transloco';
import { AuthService } from '@services/auth.service';

@Component({
  selector: 'pages-settings',
  templateUrl: './settings.component.html',
  standalone: true,
  imports: [RouterOutlet, TranslocoDirective, RouterModule],
  changeDetection: ChangeDetectionStrategy.OnPush,
})
export class SettingsComponent {
  public authService = inject(AuthService);
}
