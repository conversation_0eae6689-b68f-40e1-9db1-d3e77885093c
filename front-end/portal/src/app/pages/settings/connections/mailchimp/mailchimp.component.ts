import {
  ChangeDetectionStrategy,
  ChangeDetectorRef,
  Component,
  inject,
  OnInit,
} from '@angular/core';
import { CommonModule } from '@angular/common';
import { PaginatedResponse } from '@responses/paginated.response';
import { lastValueFrom } from 'rxjs';
import { MailchimpConnectionService } from '@api/services/settings/mailchimp-connection.service';
import { MailchimpConnection } from '@api/interfaces/settings/mailchimp-connection.interface';
import { LoaderComponent } from '@components/loader/loader.component';
import { TranslocoDirective, TranslocoService } from '@jsverse/transloco';
import { ToastService } from '@components/toast/toast.service';
import { ConfirmationModalService } from '@components/confirmation-modal/confirmation-modal.service';
import { RouterLink } from '@angular/router';

@Component({
  selector: 'pages-settings-mailchimp',
  templateUrl: './mailchimp.component.html',
  standalone: true,
  imports: [CommonModule, LoaderComponent, TranslocoDirective, RouterLink],
  changeDetection: ChangeDetectionStrategy.OnPush,
})
export class MailchimpComponent implements OnInit {
  // State
  protected isLoading = true;
  protected pageNumber?: number;
  protected paginatedResponse?: PaginatedResponse<MailchimpConnection>;

  // Injects
  private mailchimpConnectionService = inject(MailchimpConnectionService);
  private changeDetectorRef = inject(ChangeDetectorRef);
  private confirmationModalService = inject(ConfirmationModalService);
  private toastService = inject(ToastService);
  private translocoService = inject(TranslocoService);

  public ngOnInit(): void {
    this.loadMailchimpConnections();
  }

  protected async loadMailchimpConnections(page?: number): Promise<void> {
    this.isLoading = true;
    this.pageNumber = page;

    this.paginatedResponse = await lastValueFrom(
      this.mailchimpConnectionService.index(page),
    );

    this.isLoading = false;
    this.changeDetectorRef.detectChanges();
  }

  public async delete(mailchimpConnection: MailchimpConnection): Promise<void> {
    await this.confirmationModalService.confirm(
      {
        confirmed: async () => {
          await lastValueFrom(
            this.mailchimpConnectionService.destroy(mailchimpConnection.id),
          );

          this.toastService.addToast({
            title: this.translocoService.translate(
              'pages.settings.mailchimp.index.toasts.deleted.title',
            ),
            message: this.translocoService.translate(
              'pages.settings.mailchimp.index.toasts.deleted.description',
            ),
            icon: 'success',
            duration: 5000,
          });
        },
      },
      {
        title: 'pages.settings.mailchimp.index.modals.delete.title',
        description: 'pages.settings.mailchimp.index.modals.delete.description',
      },
    );
  }
}
