import { Component, DestroyRef, inject, signal } from '@angular/core';
import {
  FormControl,
  FormGroup,
  ReactiveFormsModule,
  Validators,
} from '@angular/forms';
import { LoaderComponent } from '@components/loader/loader.component';
import { Router, RouterLink } from '@angular/router';
import { TranslocoDirective, TranslocoService } from '@jsverse/transloco';
import { MailchimpConnectionService } from '@api/services/settings/mailchimp-connection.service';
import { ToastService } from '@components/toast/toast.service';
import { MailchimpRequest } from '@api/requests/settings/mailchimp.request';
import { catchError, tap } from 'rxjs';
import { takeUntilDestroyed } from '@angular/core/rxjs-interop';

interface Form {
  name: FormControl<string | null>;
  api_key: FormControl<string | null>;
}

@Component({
  selector: 'app-add',
  standalone: true,
  imports: [
    ReactiveFormsModule,
    LoaderComponent,
    RouterLink,
    TranslocoDirective,
  ],
  templateUrl: './add.component.html',
})
export class AddComponent {
  public loading = signal<boolean>(false);

  public form: FormGroup<Form> = new FormGroup<Form>({
    name: new FormControl(null, [Validators.required]),
    api_key: new FormControl(null, [Validators.required]),
  });

  private mailchimpService = inject(MailchimpConnectionService);
  private translocoService = inject(TranslocoService);
  private destroyRef = inject(DestroyRef);
  private toastService = inject(ToastService);
  private router = inject(Router);

  public submit(): void {
    if (this.loading() || this.form.invalid) {
      this.form.markAllAsTouched();
      return;
    }

    this.loading.set(false);

    this.mailchimpService
      .store({
        name: this.form.controls.name.value,
        api_key: this.form.controls.api_key.value,
      } as MailchimpRequest)
      .pipe(
        tap(() => {
          this.router.navigateByUrl('/settings/connections/mailchimp');
        }),
        catchError((err) => {
          this.toastService.addToast({
            title: this.translocoService.translate(
              'general.toasts.error.title',
            ),
            message: this.translocoService.translate(
              'general.toasts.error.description',
            ),
            icon: 'error',
            duration: 5000,
          });
          this.loading.set(false);
          return err;
        }),
        takeUntilDestroyed(this.destroyRef),
      )
      .subscribe();
  }
}
