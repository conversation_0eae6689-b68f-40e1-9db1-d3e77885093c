import {
  ChangeDetectionStrategy,
  ChangeDetectorRef,
  Component,
  inject,
  OnInit,
} from '@angular/core';
import { CommonModule } from '@angular/common';
import { TranslocoDirective } from '@jsverse/transloco';
import { ActivatedRoute, Router, RouterLink } from '@angular/router';
import { LoaderComponent } from '@components/loader/loader.component';
import { lastValueFrom } from 'rxjs';
import { FacebookConnectionService } from '@api/services/settings/facebook-connection.service';
import { FacebookConnection } from '@api/interfaces/settings/facebook-connection.interface';
import { FacebookConnectionMetadata } from '@api/interfaces/settings/facebook-connection-metadata.interface';

interface RouteParams {
  id?: number;
}

@Component({
  selector: 'pages-settings-facebook-detail',
  templateUrl: './facebook-connection-detail.component.html',
  standalone: true,
  imports: [CommonModule, TranslocoDirective, LoaderComponent, RouterLink],
  changeDetection: ChangeDetectionStrategy.OnPush,
})
export class FacebookConnectionDetailComponent implements OnInit {
  // Injects
  private router = inject(Router);
  private route = inject(ActivatedRoute);
  private facebookConnectionService = inject(FacebookConnectionService);
  private changeDetectorRef = inject(ChangeDetectorRef);

  // State
  protected facebookConnection?: FacebookConnection;
  protected metadata?: FacebookConnectionMetadata;
  protected isLoading = false;
  private routeParams: RouteParams = {};

  public ngOnInit(): void {
    this.readRouteParameters();
  }

  protected readRouteParameters(): void {
    this.route.params.subscribe((params: RouteParams) => {
      this.routeParams = params;

      if (params.id) {
        this.loadFacebookConnection();
      }
    });
  }

  protected async loadFacebookConnection(): Promise<void> {
    this.isLoading = true;

    try {
      const response = await lastValueFrom(
        this.facebookConnectionService.show(this.routeParams.id as number),
      );
      this.facebookConnection = response.data;
      this.metadata = response.metadata;
    } catch (error) {
      await this.router.navigateByUrl(`/settings/connections/facebook`);
      return;
    }

    this.isLoading = false;
    this.changeDetectorRef.detectChanges();
  }
}
