import {
  ChangeDetectionStrategy,
  ChangeDetectorRef,
  Component,
  inject,
  OnInit,
} from '@angular/core';
import { CommonModule } from '@angular/common';
import { PaginatedResponse } from '@responses/paginated.response';
import { lastValueFrom } from 'rxjs';
import { FacebookConnectionService } from '@api/services/settings/facebook-connection.service';
import { FacebookConnection } from '@api/interfaces/settings/facebook-connection.interface';
import { LoaderComponent } from '@components/loader/loader.component';
import { TranslocoDirective, TranslocoService } from '@jsverse/transloco';
import { FacebookButtonComponent } from '../../../connect/facebook-button/facebook-button.component';
import { ToastService } from '@components/toast/toast.service';
import { Router, RouterLink } from '@angular/router';
import { ConfirmationModalService } from '@components/confirmation-modal/confirmation-modal.service';

@Component({
  selector: 'pages-settings-facebook',
  templateUrl: './facebook.component.html',
  standalone: true,
  imports: [
    CommonModule,
    LoaderComponent,
    TranslocoDirective,
    FacebookButtonComponent,
    RouterLink,
  ],
  changeDetection: ChangeDetectionStrategy.OnPush,
})
export class FacebookComponent implements OnInit {
  // State
  protected isLoading = true;
  protected pageNumber?: number;
  protected paginatedResponse?: PaginatedResponse<FacebookConnection>;

  // Injects
  private facebookConnectionService = inject(FacebookConnectionService);
  private toastService = inject(ToastService);
  private router = inject(Router);
  private translocoService = inject(TranslocoService);
  private changeDetectorRef = inject(ChangeDetectorRef);
  private confirmationModalService = inject(ConfirmationModalService);

  public ngOnInit(): void {
    this.loadFacebookConnections();
  }

  protected async loadFacebookConnections(page?: number): Promise<void> {
    this.isLoading = true;
    this.pageNumber = page;

    this.paginatedResponse = await lastValueFrom(
      this.facebookConnectionService.index(page),
    );

    this.isLoading = false;
    this.changeDetectorRef.detectChanges();
  }

  public async delete(facebookConnection: FacebookConnection): Promise<void> {
    await this.confirmationModalService.confirm(
      {
        confirmed: async () => {
          await lastValueFrom(
            this.facebookConnectionService.destroy(facebookConnection.id),
          );

          this.toastService.addToast({
            title: this.translocoService.translate(
              'pages.settings.facebook.index.toasts.deleted.title',
            ),
            message: this.translocoService.translate(
              'pages.settings.facebook.index.toasts.deleted.description',
            ),
            icon: 'success',
            duration: 5000,
          });
        },
      },
      {
        title: 'pages.settings.facebook.index.modals.delete.title',
        description: 'pages.settings.facebook.index.modals.delete.description',
      },
    );
  }

  protected async loadExternalConnectInClipboard(): Promise<void> {
    await navigator.clipboard.writeText(this.generateExternalConnectUrl());

    this.toastService.addToast({
      title: this.translocoService.translate(
        'pages.settings.facebook.index.toasts.share.title',
      ),
      message: this.translocoService.translate(
        'pages.settings.facebook.index.toasts.share.description',
      ),
      icon: 'success',
      duration: 5000,
    });
  }

  protected generateExternalConnectUrl(): string {
    const urlTree = this.router.createUrlTree(['/connect'], {
      queryParams: { provider: 'facebook' },
    });
    const relativeUrl = this.router.serializeUrl(urlTree);
    const baseUrl = `${window.location.protocol}//${window.location.host}`;

    return `${baseUrl}${relativeUrl}`;
  }
}
