<div *transloco="let t; prefix: 'pages.settings.facebook.detail'" class="overflow-hidden rounded-lg bg-lkq-pure-white shadow">
  <div class="p-6 relative">

    <div class="border-b border-lkq-gray-200 pb-5 sm:flex sm:items-center sm:justify-between">
      <div>
        <h3 class="text-base font-medium leading-6 text-lkq-gray-900">{{ t('title', { name: facebookConnection?.name ?? '' }) }}</h3>
        <p class="mt-1 text-sm leading-6 text-lkq-gray-800">{{ t('description') }}</p>
      </div>
      <div class="mt-3 flex sm:ml-4 sm:mt-0 items-center">
        <span routerLink="/settings/connections/facebook" class="select-none cursor-pointer text-lkq-gray-800 hover:text-lkq-electric-blue">{{ t('back') }}</span>
      </div>
    </div>

    @if (isLoading) {
      <div class="overlay absolute inset-0 flex items-center justify-center">
        <component-loader></component-loader>
      </div>
    }

    <div class="pt-4 pb-4 border-b border-lkq-gray-200">
      <div class="text-lkq-gray-900 font-medium">Facebook Ad Accounts</div>

      @if (metadata?.connected_data?.facebook_ad_accounts?.length) {
        <ul role="list" class="divide-y divide-lkq-gray-100">
          @for(facebookAdAccount of metadata?.connected_data?.facebook_ad_accounts ?? []; track facebookAdAccount) {
            <li class="flex justify-between gap-x-6 py-2">
              <div class="flex min-w-0 gap-x-4">
                <div class="min-w-0 flex-auto">
                  <p class="text-sm font-medium leading-6 text-lkq-gray-900">{{ facebookAdAccount.name }}</p>
                  <p class="mt-1 truncate text-xs leading-5 text-lkq-gray-800">{{  facebookAdAccount.connection_type }} | {{  facebookAdAccount.connection_name }}</p>
                </div>
              </div>
              <div class="hidden shrink-0 sm:flex sm:flex-col sm:items-end sm:justify-center">
                <div class="mt-1 flex items-center gap-x-1.5">
                  <p class="text-xs leading-5 text-lkq-gray-800">{{ t('latest_sync') }} {{ facebookAdAccount.last_synced_at }}</p>
                </div>
              </div>
            </li>
          }
        </ul>
      } @else {
        <div class="pt-1">{{ t('no_connections') }}</div>
      }
    </div>

    <div class="pt-4 pb-4 border-b border-lkq-gray-200">
      <div class="text-lkq-gray-900 font-medium">Facebook Pages</div>

      @if (metadata?.connected_data?.facebook_pages?.length) {
        <ul role="list" class="divide-y divide-lkq-gray-100">
          @for(facebookPages of metadata?.connected_data?.facebook_pages ?? []; track facebookPages) {
            <li class="flex justify-between gap-x-6 py-2">
              <div class="flex min-w-0 gap-x-4">
                <div class="min-w-0 flex-auto">
                  <p class="text-sm font-medium leading-6 text-lkq-gray-900">{{ facebookPages.name }}</p>
                  <p class="mt-1 truncate text-xs leading-5 text-lkq-gray-800">{{  facebookPages.connection_type }} | {{  facebookPages.connection_name }}</p>
                </div>
              </div>
              <div class="hidden shrink-0 sm:flex sm:flex-col sm:items-end sm:justify-center">
                <div class="mt-1 flex items-center gap-x-1.5">
                  <p class="text-xs leading-5 text-lkq-gray-800">{{ t('latest_sync') }} {{ facebookPages.last_synced_at }}</p>
                </div>
              </div>
            </li>
          }
        </ul>
      } @else {
        <div class="pt-1">{{ t('no_connections') }}</div>
      }
    </div>

    <div class="pt-4">
      <div class="text-lkq-gray-900 font-medium">Instagram Accounts</div>

      @if (metadata?.connected_data?.instagram_accounts?.length) {
        <ul role="list" class="divide-y divide-lkq-gray-100">
          @for(instagramAccount of metadata?.connected_data?.instagram_accounts ?? []; track instagramAccount) {
            <li class="flex justify-between gap-x-6 py-2">
              <div class="flex min-w-0 gap-x-4">
                <div class="min-w-0 flex-auto">
                  <p class="text-sm font-medium leading-6 text-lkq-gray-900">{{ instagramAccount.username }}</p>
                  <p class="mt-1 truncate text-xs leading-5 text-lkq-gray-800">{{  instagramAccount.connection_type }} | {{  instagramAccount.connection_name }}</p>
                </div>
              </div>
              <div class="hidden shrink-0 sm:flex sm:flex-col sm:items-end sm:justify-center">
                <div class="mt-1 flex items-center gap-x-1.5">
                  <p class="text-xs leading-5 text-lkq-gray-800">{{ t('latest_sync') }} {{ instagramAccount.last_synced_at }}</p>
                </div>
              </div>
            </li>
          }
        </ul>
      } @else {
        <div class="pt-1">{{ t('no_connections') }}</div>
      }
    </div>

  </div>
</div>
