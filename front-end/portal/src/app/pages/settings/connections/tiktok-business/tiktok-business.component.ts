import {
  ChangeDetectionStrategy,
  ChangeDetectorRef,
  Component,
  inject,
  OnInit,
} from '@angular/core';
import { CommonModule } from '@angular/common';
import { TranslocoDirective, TranslocoService } from '@jsverse/transloco';
import { PaginatedResponse } from '@responses/paginated.response';
import { ToastService } from '@components/toast/toast.service';
import { Router } from '@angular/router';
import { ConfirmationModalService } from '@components/confirmation-modal/confirmation-modal.service';
import { lastValueFrom } from 'rxjs';
import { LoaderComponent } from '@components/loader/loader.component';
import { TikTokBusinessConnectionService } from '@api/services/settings/tik-tok-business-connection.service';
import { TiktokBusinessButtonComponent } from '../../../connect/tiktok-business-button/tiktok-business-button.component';
import { TikTokBusinessConnection } from '@api/interfaces/settings/tiktok-business-connection.interface';

@Component({
  selector: 'pages-settings-tiktok-business',
  templateUrl: './tiktok-business.component.html',
  standalone: true,
  imports: [
    CommonModule,
    TranslocoDirective,
    LoaderComponent,
    TiktokBusinessButtonComponent,
  ],
  changeDetection: ChangeDetectionStrategy.OnPush,
})
export class TiktokBusinessComponent implements OnInit {
  // State
  protected isLoading = true;
  protected pageNumber?: number;
  protected paginatedResponse?: PaginatedResponse<TikTokBusinessConnection>;

  // Injects
  private tikTokBusinessConnectionService = inject(
    TikTokBusinessConnectionService,
  );
  private toastService = inject(ToastService);
  private router = inject(Router);
  private translocoService = inject(TranslocoService);
  private changeDetectorRef = inject(ChangeDetectorRef);
  private confirmationModalService = inject(ConfirmationModalService);

  public ngOnInit(): void {
    this.loadTikTokConnections();
  }

  protected async loadTikTokConnections(page?: number): Promise<void> {
    this.isLoading = true;
    this.pageNumber = page;

    this.paginatedResponse = await lastValueFrom(
      this.tikTokBusinessConnectionService.index(page),
    );

    this.isLoading = false;
    this.changeDetectorRef.detectChanges();
  }

  public async delete(
    tikTokBusinessConnection: TikTokBusinessConnection,
  ): Promise<void> {
    await this.confirmationModalService.confirm(
      {
        confirmed: async () => {
          await lastValueFrom(
            this.tikTokBusinessConnectionService.destroy(
              tikTokBusinessConnection.id,
            ),
          );

          this.toastService.addToast({
            title: this.translocoService.translate(
              'pages.settings.tiktok-business.index.toasts.deleted.title',
            ),
            message: this.translocoService.translate(
              'pages.settings.tiktok-business.index.toasts.deleted.description',
            ),
            icon: 'success',
            duration: 5000,
          });
        },
      },
      {
        title: 'pages.settings.tiktok-business.index.modals.delete.title',
        description:
          'pages.settings.tiktok-business.index.modals.delete.description',
      },
    );
  }

  protected async loadExternalConnectInClipboard(): Promise<void> {
    await navigator.clipboard.writeText(this.generateExternalConnectUrl());

    this.toastService.addToast({
      title: this.translocoService.translate(
        'pages.settings.tiktok-business.index.toasts.share.title',
      ),
      message: this.translocoService.translate(
        'pages.settings.tiktok-business.index.toasts.share.description',
      ),
      icon: 'success',
      duration: 5000,
    });
  }

  protected generateExternalConnectUrl(): string {
    const urlTree = this.router.createUrlTree(['/connect'], {
      queryParams: { provider: 'tiktok-business' },
    });
    const relativeUrl = this.router.serializeUrl(urlTree);
    const baseUrl = `${window.location.protocol}//${window.location.host}`;

    return `${baseUrl}${relativeUrl}`;
  }
}
