<div *transloco="let t; prefix: 'pages.settings.tiktok-business.index'" class="overflow-hidden rounded-lg bg-lkq-pure-white shadow">
  <div class="p-6">

    <div class="border-b border-lkq-gray-200 pb-5 sm:flex sm:items-center sm:justify-between">
      <h3 class="text-base font-medium leading-6 text-lkq-gray-900">{{ t('title') }}</h3>
      @if (false) {
        <div class="tw-hidden mt-3 flex sm:ml-4 sm:mt-0">
          <button (click)="loadExternalConnectInClipboard()" type="button" class="inline-flex items-center rounded-md px-3 py-2 text-sm font-medium text-lkq-gray-900 shadow-sm ring-1 ring-inset ring-lkq-gray-200 hover:bg-lkq-gray-200">
            {{ t('share') }}
          </button>
          <div class="ml-3" style="height: 40px;">
            <pages-connect-tiktok-business-button/>
          </div>
        </div>
      }
    </div>

    @if (isLoading) {
      <div class="my-4 flex justify-center">
        <component-loader></component-loader>
      </div>
    }

    @if (!isLoading && paginatedResponse) {
      @if (paginatedResponse.meta.total > 0) {
        <ul role="list" class="divide-y divide-lkq-gray-100">
          @for (tikTokBusinessConnection of paginatedResponse.data; track tikTokBusinessConnection.id) {
            <li class="flex justify-between gap-x-6 py-5">
              <div class="flex min-w-0 gap-x-4">
                <div class="min-w-0 flex-auto">
                  <p class="text-sm font-medium leading-6 text-lkq-gray-900">{{ tikTokBusinessConnection.name }}</p>
                  <p class="mt-1 truncate text-xs leading-5 text-lkq-gray-800">{{ tikTokBusinessConnection.email }}</p>
                </div>
              </div>
              <div class="hidden shrink-0 sm:flex sm:flex-col sm:items-end sm:justify-center">
                <div class="mt-1 flex items-center gap-x-1.5">
                  <p class="text-xs leading-5 text-lkq-gray-800">
                    <span (click)="delete(tikTokBusinessConnection)" class="cursor-pointer text-red-700 hover:text-red-900">{{ t('delete') }}</span>
                  </p>
                </div>
              </div>
            </li>
          }
        </ul>

        <nav *transloco="let t; prefix: 'pagination'" class="flex items-center justify-between border-t border-gray-200 px-4 sm:px-0">
          <div class="-mt-px flex w-0 flex-1">
            @if (paginatedResponse.meta.current_page > 1) {
              <a (click)="loadTikTokConnections(paginatedResponse.meta.current_page - 1)" class="inline-flex items-center border-t-2 border-transparent pr-1 pt-4 text-sm font-medium text-lkq-gray-800 hover:border-lkq-gray-200 hover:text-lkq-deep-black hover:underline">
                <svg class="mr-3 h-5 w-5 text-lkq-gray-800" viewBox="0 0 20 20" fill="currentColor" aria-hidden="true">
                  <path fill-rule="evenodd" d="M18 10a.75.75 0 01-.75.75H4.66l2.1 1.95a.75.75 0 11-1.02 1.1l-3.5-3.25a.75.75 0 010-1.1l3.5-3.25a.75.75 0 111.02 1.1l-2.1 1.95h12.59A.75.75 0 0118 10z" clip-rule="evenodd" />
                </svg>
                {{ t('previous') }}
              </a>
            }
          </div>
          <div class="hidden sm:block">
            <p class="text-sm text-lkq-gray-800 pt-4">
              {{ t('shows') }}
              <span class="font-medium">{{ paginatedResponse.meta.from }}</span>
              {{ t('until') }}
              <span class="font-medium">{{ paginatedResponse.meta.to }}</span>
              {{ t('of') }}
              <span class="font-medium">{{ paginatedResponse.meta.total }}</span>
              {{ t('results') }}
            </p>
          </div>
          <div class="-mt-px flex w-0 flex-1 justify-end">
            @if (paginatedResponse.meta.current_page < paginatedResponse.meta.last_page) {
              <a (click)="loadTikTokConnections(paginatedResponse.meta.current_page + 1)" class="inline-flex items-center border-t-2 border-transparent pl-1 pt-4 text-sm font-medium text-lkq-gray-800 hover:border-lkq-gray-200 hover:text-lkq-deep-black hover:underline">
                {{ t('next') }}
                <svg class="ml-3 h-5 w-5 text-lkq-gray-800" viewBox="0 0 20 20" fill="currentColor" aria-hidden="true">
                  <path fill-rule="evenodd" d="M2 10a.75.75 0 01.75-.75h12.59l-2.1-1.95a.75.75 0 111.02-1.1l3.5 3.25a.75.75 0 010 1.1l-3.5 3.25a.75.75 0 11-1.02-1.1l2.1-1.95H2.75A.75.75 0 012 10z" clip-rule="evenodd" />
                </svg>
              </a>
            }
          </div>
        </nav>
      } @else {
        <div class="text-center pt-4">
          {{ t('no_accounts') }}
        </div>
      }
    }
  </div>
</div>
