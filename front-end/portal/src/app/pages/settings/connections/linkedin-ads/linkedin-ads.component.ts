import {
  ChangeDetectionStrategy,
  ChangeDetectorRef,
  Component,
  inject,
  OnInit,
} from '@angular/core';
import { CommonModule } from '@angular/common';
import { LoaderComponent } from '@components/loader/loader.component';
import { TranslocoDirective, TranslocoService } from '@jsverse/transloco';
import { Router, RouterLink } from '@angular/router';
import { PaginatedResponse } from '@responses/paginated.response';
import { ToastService } from '@components/toast/toast.service';
import { ConfirmationModalService } from '@components/confirmation-modal/confirmation-modal.service';
import { lastValueFrom } from 'rxjs';
import { LinkedInAdsConnection } from '@api/interfaces/settings/linked-in-ads-connection.interface';
import { LinkedInAdsConnectionService } from '@api/services/settings/linked-in-ads-connection.service';
import { LinkedinButtonComponent } from '../../../connect/linkedin-button/linkedin-button.component';

@Component({
  selector: 'pages-settings-linkedin-ads',
  templateUrl: './linkedin-ads.component.html',
  standalone: true,
  imports: [
    CommonModule,
    LoaderComponent,
    TranslocoDirective,
    RouterLink,
    LinkedinButtonComponent,
  ],
  changeDetection: ChangeDetectionStrategy.OnPush,
})
export class LinkedinAdsComponent implements OnInit {
  // State
  protected isLoading = true;
  protected pageNumber?: number;
  protected paginatedResponse?: PaginatedResponse<LinkedInAdsConnection>;

  // Injects
  private linkedInAdsConnectionService = inject(LinkedInAdsConnectionService);
  private toastService = inject(ToastService);
  private router = inject(Router);
  private translocoService = inject(TranslocoService);
  private changeDetectorRef = inject(ChangeDetectorRef);
  private confirmationModalService = inject(ConfirmationModalService);

  public ngOnInit(): void {
    this.loadLinkedInAdsConnections();
  }

  protected async loadLinkedInAdsConnections(page?: number): Promise<void> {
    this.isLoading = true;
    this.pageNumber = page;

    this.paginatedResponse = await lastValueFrom(
      this.linkedInAdsConnectionService.index(page),
    );

    this.isLoading = false;
    this.changeDetectorRef.detectChanges();
  }

  public async delete(
    linkedInAdsConnection: LinkedInAdsConnection,
  ): Promise<void> {
    await this.confirmationModalService.confirm(
      {
        confirmed: async () => {
          await lastValueFrom(
            this.linkedInAdsConnectionService.destroy(linkedInAdsConnection.id),
          );

          this.toastService.addToast({
            title: this.translocoService.translate(
              'pages.settings.linkedin-ads.index.toasts.deleted.title',
            ),
            message: this.translocoService.translate(
              'pages.settings.linkedin-ads.index.toasts.deleted.description',
            ),
            icon: 'success',
            duration: 5000,
          });
        },
      },
      {
        title: 'pages.settings.linkedin-ads.index.modals.delete.title',
        description:
          'pages.settings.linkedin-ads.index.modals.delete.description',
      },
    );
  }

  protected async loadExternalConnectInClipboard(): Promise<void> {
    await navigator.clipboard.writeText(this.generateExternalConnectUrl());

    this.toastService.addToast({
      title: this.translocoService.translate(
        'pages.settings.linkedin-ads.index.toasts.share.title',
      ),
      message: this.translocoService.translate(
        'pages.settings.linkedin-ads.index.toasts.share.description',
      ),
      icon: 'success',
      duration: 5000,
    });
  }

  protected generateExternalConnectUrl(): string {
    const urlTree = this.router.createUrlTree(['/connect'], {
      queryParams: { provider: 'linkedin-ads' },
    });
    const relativeUrl = this.router.serializeUrl(urlTree);
    const baseUrl = `${window.location.protocol}//${window.location.host}`;

    return `${baseUrl}${relativeUrl}`;
  }
}
