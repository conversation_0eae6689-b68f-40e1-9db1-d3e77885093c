<div *transloco="let t; prefix: 'pages.settings.linkedin-ads.detail'" class="overflow-hidden rounded-lg bg-lkq-pure-white shadow">
  <div class="p-6 relative">

    <div class="border-b border-lkq-gray-200 pb-5 sm:flex sm:items-center sm:justify-between">
      <div>
        <h3 class="text-base font-medium leading-6 text-lkq-gray-900">{{ t('title', { name: linkedInAdsConnection?.name ?? '' }) }}</h3>
        <p class="mt-1 text-sm leading-6 text-lkq-gray-800">{{ t('description') }}</p>
      </div>
      <div class="mt-3 flex sm:ml-4 sm:mt-0 items-center">
        <span routerLink="/settings/connections/linkedin-ads" class="select-none cursor-pointer text-lkq-gray-800 hover:text-lkq-electric-blue">{{ t('back') }}</span>
      </div>
    </div>

    @if (isLoading) {
      <div class="overlay absolute inset-0 flex items-center justify-center">
        <component-loader></component-loader>
      </div>
    }

    <div class="pt-4 pb-4 border-b border-lkq-gray-200">
      <div class="text-lkq-gray-900 font-medium">LinkedIn Ad Accounts</div>

      @if (metadata?.connected_data?.linked_in_ad_accounts?.length) {
        <ul role="list" class="divide-y divide-lkq-gray-100">
          @for(linkedInAdAccount of metadata?.connected_data?.linked_in_ad_accounts ?? []; track linkedInAdAccount) {
            <li class="flex justify-between gap-x-6 py-2">
              <div class="flex min-w-0 gap-x-4">
                <div class="min-w-0 flex-auto">
                  <p class="text-sm font-medium leading-6 text-lkq-gray-900">{{ linkedInAdAccount.name }}</p>
                  <p class="mt-1 truncate text-xs leading-5 text-lkq-gray-800">{{  linkedInAdAccount.connection_type }} | {{  linkedInAdAccount.connection_name }}</p>
                </div>
              </div>
              <div class="hidden shrink-0 sm:flex sm:flex-col sm:items-end sm:justify-center">
                <div class="mt-1 flex items-center gap-x-1.5">
                  <p class="text-xs leading-5 text-lkq-gray-800">{{ t('latest_sync') }} {{ linkedInAdAccount.last_synced_at }}</p>
                </div>
              </div>
            </li>
          }
        </ul>
      } @else {
        <div class="pt-1">{{ t('no_connections') }}</div>
      }
    </div>

  </div>
</div>
