import {
  ChangeDetectionStrategy,
  ChangeDetectorRef,
  Component,
  inject,
  OnInit,
} from '@angular/core';
import { CommonModule } from '@angular/common';
import { TranslocoDirective } from '@jsverse/transloco';
import { ActivatedRoute, Router, RouterLink } from '@angular/router';
import { LoaderComponent } from '@components/loader/loader.component';
import { lastValueFrom } from 'rxjs';
import { LinkedInAdsConnectionService } from '@api/services/settings/linked-in-ads-connection.service';
import { LinkedInAdsConnection } from '@api/interfaces/settings/linked-in-ads-connection.interface';
import { LinkedInAdsConnectionMetadata } from '@api/interfaces/settings/linked-in-ads-connection-metadata.interface';

interface RouteParams {
  id?: number;
}

@Component({
  selector: 'pages-settings-linkedin-ads-detail',
  templateUrl: './linked-in-ads-connection-detail.component.html',
  standalone: true,
  imports: [CommonModule, TranslocoDirective, LoaderComponent, RouterLink],
  changeDetection: ChangeDetectionStrategy.OnPush,
})
export class LinkedInAdsConnectionDetailComponent implements OnInit {
  // Injects
  private router = inject(Router);
  private route = inject(ActivatedRoute);
  private linkedInAdsConnectionService = inject(LinkedInAdsConnectionService);
  private changeDetectorRef = inject(ChangeDetectorRef);

  // State
  protected linkedInAdsConnection?: LinkedInAdsConnection;
  protected metadata?: LinkedInAdsConnectionMetadata;
  protected isLoading = false;
  private routeParams: RouteParams = {};

  public ngOnInit(): void {
    this.readRouteParameters();
  }

  protected readRouteParameters(): void {
    this.route.params.subscribe((params: RouteParams) => {
      this.routeParams = params;

      if (params.id) {
        this.loadLinkedInAdsConnection();
      }
    });
  }

  protected async loadLinkedInAdsConnection(): Promise<void> {
    this.isLoading = true;

    try {
      const response = await lastValueFrom(
        this.linkedInAdsConnectionService.show(this.routeParams.id as number),
      );
      this.linkedInAdsConnection = response.data;
      this.metadata = response.metadata;
    } catch (error) {
      await this.router.navigateByUrl(`/settings/connections/linkedin-ads`);
      return;
    }

    this.isLoading = false;
    this.changeDetectorRef.detectChanges();
  }
}
