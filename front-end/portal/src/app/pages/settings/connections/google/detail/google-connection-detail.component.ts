import {
  ChangeDetectionStrategy,
  ChangeDetectorRef,
  Component,
  inject,
  OnInit,
} from '@angular/core';
import { CommonModule } from '@angular/common';
import { TranslocoDirective } from '@jsverse/transloco';
import { ActivatedRoute, Router, RouterLink } from '@angular/router';
import { LoaderComponent } from '@components/loader/loader.component';
import { lastValueFrom } from 'rxjs';
import { GoogleConnectionService } from '@api/services/settings/google-connection.service';
import { GoogleConnection } from '@api/interfaces/settings/google-connection.interface';
import { GoogleConnectionMetadata } from '@api/interfaces/settings/google-connection-metadata.interface';

interface RouteParams {
  id?: number;
}

@Component({
  selector: 'pages-settings-google-detail',
  templateUrl: './google-connection-detail.component.html',
  standalone: true,
  imports: [CommonModule, TranslocoDirective, LoaderComponent, RouterLink],
  changeDetection: ChangeDetectionStrategy.OnPush,
})
export class GoogleConnectionDetailComponent implements OnInit {
  // Injects
  private router = inject(Router);
  private route = inject(ActivatedRoute);
  private googleConnectionService = inject(GoogleConnectionService);
  private changeDetectorRef = inject(ChangeDetectorRef);

  // State
  protected googleConnection?: GoogleConnection;
  protected metadata?: GoogleConnectionMetadata;
  protected isLoading = false;
  private routeParams: RouteParams = {};

  public ngOnInit(): void {
    this.readRouteParameters();
  }

  protected readRouteParameters(): void {
    this.route.params.subscribe((params: RouteParams) => {
      this.routeParams = params;

      if (params.id) {
        this.loadGoogleConnection();
      }
    });
  }

  protected async loadGoogleConnection(): Promise<void> {
    this.isLoading = true;

    try {
      const response = await lastValueFrom(
        this.googleConnectionService.show(this.routeParams.id as number),
      );
      this.googleConnection = response.data;
      this.metadata = response.metadata;
    } catch (error) {
      await this.router.navigateByUrl(`/settings/connections/google`);
      return;
    }

    this.isLoading = false;
    this.changeDetectorRef.detectChanges();
  }
}
