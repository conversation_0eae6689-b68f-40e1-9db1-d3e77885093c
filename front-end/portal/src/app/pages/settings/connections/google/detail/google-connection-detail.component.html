<div *transloco="let t; prefix: 'pages.settings.google.detail'" class="overflow-hidden rounded-lg bg-lkq-pure-white shadow">
  <div class="p-6 relative">

    <div class="border-b border-lkq-gray-200 pb-5 sm:flex sm:items-center sm:justify-between">
      <div>
        <h3 class="text-base font-medium leading-6 text-lkq-gray-900">{{ t('title', { name: googleConnection?.name ?? '' }) }}</h3>
        <p class="mt-1 text-sm leading-6 text-lkq-gray-800">{{ t('description') }}</p>
      </div>
      <div class="mt-3 flex sm:ml-4 sm:mt-0 items-center">
        <span routerLink="/settings/connections/google" class="select-none cursor-pointer text-lkq-gray-800 hover:text-lkq-electric-blue">{{ t('back') }}</span>
      </div>
    </div>

    @if (isLoading) {
      <div class="overlay absolute inset-0 flex items-center justify-center">
        <component-loader></component-loader>
      </div>
    }

    <div class="pt-4 pb-4 border-b border-lkq-gray-200">
      <div class="text-lkq-gray-900 font-medium">Google Ad Accounts</div>

      @if (metadata?.connected_data?.google_ad_accounts?.length) {
        <ul role="list" class="divide-y divide-lkq-gray-100">
          @for(googleAdAccount of metadata?.connected_data?.google_ad_accounts ?? []; track googleAdAccount) {
            <li class="flex justify-between gap-x-6 py-2">
              <div class="flex min-w-0 gap-x-4">
                <div class="min-w-0 flex-auto">
                  <p class="text-sm font-medium leading-6 text-lkq-gray-900">{{ googleAdAccount.name }}</p>
                  <p class="mt-1 truncate text-xs leading-5 text-lkq-gray-800">{{  googleAdAccount.connection_type }} | {{  googleAdAccount.connection_name }}</p>
                </div>
              </div>
              <div class="hidden shrink-0 sm:flex sm:flex-col sm:items-end sm:justify-center">
                <div class="mt-1 flex items-center gap-x-1.5">
                  <p class="text-xs leading-5 text-lkq-gray-800">{{ t('latest_sync') }} {{ googleAdAccount.last_synced_at }}</p>
                </div>
              </div>
            </li>
          }
        </ul>
      } @else {
        <div class="pt-1">{{ t('no_connections') }}</div>
      }
    </div>


    <div class="pt-4">
      <div class="text-lkq-gray-900 font-medium">Google Analytics Properties</div>

      @if (metadata?.connected_data?.google_analytics_properties?.length) {
        <ul role="list" class="divide-y divide-lkq-gray-100">
          @for(googleAnalyticsProperties of metadata?.connected_data?.google_analytics_properties ?? []; track googleAnalyticsProperties) {
            <li class="flex justify-between gap-x-6 py-2">
              <div class="flex min-w-0 gap-x-4">
                <div class="min-w-0 flex-auto">
                  <p class="text-sm font-medium leading-6 text-lkq-gray-900">{{ googleAnalyticsProperties.name }}</p>
                  <p class="mt-1 truncate text-xs leading-5 text-lkq-gray-800">{{  googleAnalyticsProperties.connection_type }} | {{  googleAnalyticsProperties.connection_name }}</p>
                </div>
              </div>
              <div class="hidden shrink-0 sm:flex sm:flex-col sm:items-end sm:justify-center">
                <div class="mt-1 flex items-center gap-x-1.5">
                  <p class="text-xs leading-5 text-lkq-gray-800">{{ t('latest_sync') }} {{ googleAnalyticsProperties.last_synced_at }}</p>
                </div>
              </div>
            </li>
          }
        </ul>
      } @else {
        <div class="pt-1">{{ t('no_connections') }}</div>
      }
    </div>

  </div>
</div>
