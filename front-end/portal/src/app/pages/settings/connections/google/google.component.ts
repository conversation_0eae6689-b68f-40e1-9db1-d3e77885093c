import {
  ChangeDetectionStrategy,
  ChangeDetectorRef,
  Component,
  inject,
  OnInit,
} from '@angular/core';
import { CommonModule } from '@angular/common';
import { PaginatedResponse } from '@responses/paginated.response';
import { lastValueFrom } from 'rxjs';
import { GoogleConnectionService } from '@api/services/settings/google-connection.service';
import { GoogleConnection } from '@api/interfaces/settings/google-connection.interface';
import { LoaderComponent } from '@components/loader/loader.component';
import { TranslocoDirective, TranslocoService } from '@jsverse/transloco';
import { GoogleButtonComponent } from '../../../connect/google-button/google-button.component';
import { ToastService } from '@components/toast/toast.service';
import { Router, RouterLink } from '@angular/router';
import { ConfirmationModalService } from '@components/confirmation-modal/confirmation-modal.service';

@Component({
  selector: 'pages-settings-google',
  templateUrl: './google.component.html',
  standalone: true,
  imports: [
    CommonModule,
    LoaderComponent,
    TranslocoDirective,
    GoogleButtonComponent,
    RouterLink,
  ],
  changeDetection: ChangeDetectionStrategy.OnPush,
})
export class GoogleComponent implements OnInit {
  // State
  protected isLoading = true;
  protected pageNumber?: number;
  protected paginatedResponse?: PaginatedResponse<GoogleConnection>;

  // Injects
  private googleConnectionService = inject(GoogleConnectionService);
  private toastService = inject(ToastService);
  private router = inject(Router);
  private translocoService = inject(TranslocoService);
  private changeDetectorRef = inject(ChangeDetectorRef);
  private confirmationModalService = inject(ConfirmationModalService);

  public ngOnInit(): void {
    this.loadGoogleConnections();
  }

  protected async loadGoogleConnections(page?: number): Promise<void> {
    this.isLoading = true;
    this.pageNumber = page;

    this.paginatedResponse = await lastValueFrom(
      this.googleConnectionService.index(page),
    );

    this.isLoading = false;
    this.changeDetectorRef.detectChanges();
  }

  public async delete(googleConnection: GoogleConnection): Promise<void> {
    await this.confirmationModalService.confirm(
      {
        confirmed: async () => {
          await lastValueFrom(
            this.googleConnectionService.destroy(googleConnection.id),
          );

          this.toastService.addToast({
            title: this.translocoService.translate(
              'pages.settings.google.index.toasts.deleted.title',
            ),
            message: this.translocoService.translate(
              'pages.settings.google.index.toasts.deleted.description',
            ),
            icon: 'success',
            duration: 5000,
          });
        },
      },
      {
        title: 'pages.settings.google.index.modals.delete.title',
        description: 'pages.settings.google.index.modals.delete.description',
      },
    );
  }

  protected async loadExternalConnectInClipboard(): Promise<void> {
    await navigator.clipboard.writeText(this.generateExternalConnectUrl());

    this.toastService.addToast({
      title: this.translocoService.translate(
        'pages.settings.google.index.toasts.share.title',
      ),
      message: this.translocoService.translate(
        'pages.settings.google.index.toasts.share.description',
      ),
      icon: 'success',
      duration: 5000,
    });
  }

  protected generateExternalConnectUrl(): string {
    const urlTree = this.router.createUrlTree(['/connect'], {
      queryParams: { provider: 'google' },
    });
    const relativeUrl = this.router.serializeUrl(urlTree);
    const baseUrl = `${window.location.protocol}//${window.location.host}`;

    return `${baseUrl}${relativeUrl}`;
  }
}
