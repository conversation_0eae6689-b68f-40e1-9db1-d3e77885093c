import {
  ChangeDetectionStrategy,
  ChangeDetectorRef,
  Component,
  inject,
  OnInit,
} from '@angular/core';
import { CommonModule } from '@angular/common';
import { TranslocoDirective, TranslocoService } from '@jsverse/transloco';
import { PaginatedResponse } from '@responses/paginated.response';
import { ToastService } from '@components/toast/toast.service';
import { Router } from '@angular/router';
import { ConfirmationModalService } from '@components/confirmation-modal/confirmation-modal.service';
import { lastValueFrom } from 'rxjs';
import { TikTokOrganicConnectionService } from '@api/services/settings/tik-tok-organic-connection.service';
import { TikTokOrganicConnection } from '@api/interfaces/settings/tiktok-organic-connection.interface';
import { LoaderComponent } from '@components/loader/loader.component';
import { TiktokOrganicButtonComponent } from '../../../connect/tiktok-organic-button/tiktok-organic-button.component';

@Component({
  selector: 'pages-settings-tiktok-organic',
  templateUrl: './tiktok-organic.component.html',
  standalone: true,
  imports: [
    CommonModule,
    TranslocoDirective,
    LoaderComponent,
    TiktokOrganicButtonComponent,
  ],
  changeDetection: ChangeDetectionStrategy.OnPush,
})
export class TiktokOrganicComponent implements OnInit {
  // State
  protected isLoading = true;
  protected pageNumber?: number;
  protected paginatedResponse?: PaginatedResponse<TikTokOrganicConnection>;

  // Injects
  private tikTokOrganicConnectionService = inject(
    TikTokOrganicConnectionService,
  );
  private toastService = inject(ToastService);
  private router = inject(Router);
  private translocoService = inject(TranslocoService);
  private changeDetectorRef = inject(ChangeDetectorRef);
  private confirmationModalService = inject(ConfirmationModalService);

  public ngOnInit(): void {
    this.loadTikTokConnections();
  }

  protected async loadTikTokConnections(page?: number): Promise<void> {
    this.isLoading = true;
    this.pageNumber = page;

    this.paginatedResponse = await lastValueFrom(
      this.tikTokOrganicConnectionService.index(page),
    );

    this.isLoading = false;
    this.changeDetectorRef.detectChanges();
  }

  public async delete(
    tikTokOrganicConnection: TikTokOrganicConnection,
  ): Promise<void> {
    await this.confirmationModalService.confirm(
      {
        confirmed: async () => {
          await lastValueFrom(
            this.tikTokOrganicConnectionService.destroy(
              tikTokOrganicConnection.id,
            ),
          );

          this.toastService.addToast({
            title: this.translocoService.translate(
              'pages.settings.tiktok-organic.index.toasts.deleted.title',
            ),
            message: this.translocoService.translate(
              'pages.settings.tiktok-organic.index.toasts.deleted.description',
            ),
            icon: 'success',
            duration: 5000,
          });
        },
      },
      {
        title: 'pages.settings.tiktok-organic.index.modals.delete.title',
        description:
          'pages.settings.tiktok-organic.index.modals.delete.description',
      },
    );
  }

  protected async loadExternalConnectInClipboard(): Promise<void> {
    await navigator.clipboard.writeText(this.generateExternalConnectUrl());

    this.toastService.addToast({
      title: this.translocoService.translate(
        'pages.settings.tiktok-organic.index.toasts.share.title',
      ),
      message: this.translocoService.translate(
        'pages.settings.tiktok-organic.index.toasts.share.description',
      ),
      icon: 'success',
      duration: 5000,
    });
  }

  protected generateExternalConnectUrl(): string {
    const urlTree = this.router.createUrlTree(['/connect'], {
      queryParams: { provider: 'tiktok-organic' },
    });
    const relativeUrl = this.router.serializeUrl(urlTree);
    const baseUrl = `${window.location.protocol}//${window.location.host}`;

    return `${baseUrl}${relativeUrl}`;
  }
}
