<div *transloco="let t; prefix: 'pages.settings.google-business-profiles.index'" class="overflow-hidden rounded-lg bg-lkq-pure-white shadow">
  <div class="p-6 pb-0">

    <div class="border-lkq-gray-200 pb-5 sm:flex sm:items-center sm:justify-between">
      <h3 class="text-base font-medium leading-6 text-lkq-gray-900">{{ t('title') }}</h3>
      <div class="mt-3 flex sm:ml-4 sm:mt-0">
        <button (click)="loadExternalConnectInClipboard()" type="button" class="inline-flex items-center rounded-md px-3 py-2 text-sm font-medium text-lkq-gray-900 shadow-sm ring-1 ring-inset ring-lkq-gray-200 hover:bg-lkq-gray-200">
          {{ t('share') }}
        </button>
        <div class="ml-3">
          <pages-connect-google-button [isBusinessProfiles]="true"></pages-connect-google-button>
        </div>
      </div>
    </div>
  </div>
</div>
