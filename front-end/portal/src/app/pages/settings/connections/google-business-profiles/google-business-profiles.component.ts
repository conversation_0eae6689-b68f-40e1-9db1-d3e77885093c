import { Component, inject } from '@angular/core';
import { GoogleButtonComponent } from '../../../connect/google-button/google-button.component';
import { TranslocoDirective, TranslocoService } from '@jsverse/transloco';
import { ConfigService } from '@services/config.service';
import { ToastService } from '@components/toast/toast.service';

@Component({
  selector: 'app-google-business-profiles',
  standalone: true,
  imports: [GoogleButtonComponent, TranslocoDirective],
  templateUrl: './google-business-profiles.component.html',
})
export class GoogleBusinessProfilesComponent {
  private configService = inject(ConfigService);
  private toastService = inject(ToastService);
  private translocoService = inject(TranslocoService);

  protected async loadExternalConnectInClipboard(): Promise<void> {
    if (!this.configService.config?.environment.googleBpUrl) {
      return;
    }

    await navigator.clipboard.writeText(
      this.configService.config.environment.googleBpUrl,
    );

    this.toastService.addToast({
      title: this.translocoService.translate(
        'pages.settings.google-business-profiles.index.toasts.share.title',
      ),
      message: this.translocoService.translate(
        'pages.settings.google-business-profiles.index.toasts.share.description',
      ),
      icon: 'success',
      duration: 5000,
    });
  }
}
