import {
  ChangeDetectionStrategy,
  ChangeDetectorRef,
  Component,
  inject,
  OnInit,
} from '@angular/core';
import { CommonModule } from '@angular/common';
import { TranslocoDirective } from '@jsverse/transloco';
import { ActivatedRoute, Router, RouterLink } from '@angular/router';
import { LoaderComponent } from '@components/loader/loader.component';
import { lastValueFrom } from 'rxjs';
import { YoutubeConnectionService } from '@api/services/settings/youtube-connection.service';
import { YoutubeConnection } from '@api/interfaces/settings/youtube-connection.interface';
import { YoutubeConnectionMetadata } from '@api/interfaces/settings/youtube-connection-metadata.interface';

interface RouteParams {
  id?: number;
}

@Component({
  selector: 'pages-settings-youtube-detail',
  templateUrl: './youtube-connection-detail.component.html',
  standalone: true,
  imports: [CommonModule, TranslocoDirective, LoaderComponent, RouterLink],
  changeDetection: ChangeDetectionStrategy.OnPush,
})
export class YoutubeConnectionDetailComponent implements OnInit {
  // Injects
  private router = inject(Router);
  private route = inject(ActivatedRoute);
  private youtubeConnectionService = inject(YoutubeConnectionService);
  private changeDetectorRef = inject(ChangeDetectorRef);

  // State
  protected youtubeConnection?: YoutubeConnection;
  protected metadata?: YoutubeConnectionMetadata;
  protected isLoading = false;
  private routeParams: RouteParams = {};

  public ngOnInit(): void {
    this.readRouteParameters();
  }

  protected readRouteParameters(): void {
    this.route.params.subscribe((params: RouteParams) => {
      this.routeParams = params;

      if (params.id) {
        this.loadYoutubeConnection();
      }
    });
  }

  protected async loadYoutubeConnection(): Promise<void> {
    this.isLoading = true;

    try {
      const response = await lastValueFrom(
        this.youtubeConnectionService.show(this.routeParams.id as number),
      );
      this.youtubeConnection = response.data;
      this.metadata = response.metadata;
    } catch (error) {
      await this.router.navigateByUrl(`/settings/connections/youtube`);
      return;
    }

    this.isLoading = false;
    this.changeDetectorRef.detectChanges();
  }
}
