import {
  ChangeDetectionStrategy,
  ChangeDetectorRef,
  Component,
  inject,
  OnInit,
} from '@angular/core';
import { CommonModule } from '@angular/common';
import { PaginatedResponse } from '@responses/paginated.response';
import { lastValueFrom } from 'rxjs';
import { YoutubeConnectionService } from '@api/services/settings/youtube-connection.service';
import { YoutubeConnection } from '@api/interfaces/settings/youtube-connection.interface';
import { LoaderComponent } from '@components/loader/loader.component';
import { TranslocoDirective, TranslocoService } from '@jsverse/transloco';
import { ToastService } from '@components/toast/toast.service';
import { Router, RouterLink } from '@angular/router';
import { ConfirmationModalService } from '@components/confirmation-modal/confirmation-modal.service';
import { YoutubeGoogleButtonComponent } from '../../../connect/youtube-google-button/youtube-google-button.component';

@Component({
  selector: 'pages-settings-youtube',
  templateUrl: './youtube.component.html',
  standalone: true,
  imports: [
    CommonModule,
    LoaderComponent,
    TranslocoDirective,
    RouterLink,
    YoutubeGoogleButtonComponent,
  ],
  changeDetection: ChangeDetectionStrategy.OnPush,
})
export class YoutubeComponent implements OnInit {
  // State
  protected isLoading = true;
  protected pageNumber?: number;
  protected paginatedResponse?: PaginatedResponse<YoutubeConnection>;

  // Injects
  private youtubeConnectionService = inject(YoutubeConnectionService);
  private toastService = inject(ToastService);
  private router = inject(Router);
  private translocoService = inject(TranslocoService);
  private changeDetectorRef = inject(ChangeDetectorRef);
  private confirmationModalService = inject(ConfirmationModalService);

  public ngOnInit(): void {
    this.loadYoutubeConnections();
  }

  protected async loadYoutubeConnections(page?: number): Promise<void> {
    this.isLoading = true;
    this.pageNumber = page;

    this.paginatedResponse = await lastValueFrom(
      this.youtubeConnectionService.index(page),
    );

    this.isLoading = false;
    this.changeDetectorRef.detectChanges();
  }

  public async delete(youtubeConnection: YoutubeConnection): Promise<void> {
    await this.confirmationModalService.confirm(
      {
        confirmed: async () => {
          await lastValueFrom(
            this.youtubeConnectionService.destroy(youtubeConnection.id),
          );

          this.toastService.addToast({
            title: this.translocoService.translate(
              'pages.settings.youtube.index.toasts.deleted.title',
            ),
            message: this.translocoService.translate(
              'pages.settings.youtube.index.toasts.deleted.description',
            ),
            icon: 'success',
            duration: 5000,
          });
        },
      },
      {
        title: 'pages.settings.youtube.index.modals.delete.title',
        description: 'pages.settings.youtube.index.modals.delete.description',
      },
    );
  }

  protected async loadExternalConnectInClipboard(): Promise<void> {
    await navigator.clipboard.writeText(this.generateExternalConnectUrl());

    this.toastService.addToast({
      title: this.translocoService.translate(
        'pages.settings.youtube.index.toasts.share.title',
      ),
      message: this.translocoService.translate(
        'pages.settings.youtube.index.toasts.share.description',
      ),
      icon: 'success',
      duration: 5000,
    });
  }

  protected generateExternalConnectUrl(): string {
    const urlTree = this.router.createUrlTree(['/connect'], {
      queryParams: { provider: 'youtube' },
    });
    const relativeUrl = this.router.serializeUrl(urlTree);
    const baseUrl = `${window.location.protocol}//${window.location.host}`;

    return `${baseUrl}${relativeUrl}`;
  }
}
