import {
  ChangeDetectionStrategy,
  ChangeDetectorRef,
  Component,
  inject,
  OnInit,
} from '@angular/core';
import { CommonModule } from '@angular/common';
import { TranslocoDirective } from '@jsverse/transloco';
import { ActivatedRoute, Router, RouterLink } from '@angular/router';
import { LoaderComponent } from '@components/loader/loader.component';
import { lastValueFrom } from 'rxjs';
import { LinkedInCommunityConnectionService } from '@api/services/settings/linked-in-community-connection.service';
import { LinkedInCommunityConnection } from '@api/interfaces/settings/linked-in-community-connection.interface';
import { LinkedInCommunityConnectionMetadata } from '@api/interfaces/settings/linked-in-community-connection-metadata.interface';

interface RouteParams {
  id?: number;
}

@Component({
  selector: 'pages-settings-linkedin-community-detail',
  templateUrl: './linked-in-community-connection-detail.component.html',
  standalone: true,
  imports: [CommonModule, TranslocoDirective, LoaderComponent, RouterLink],
  changeDetection: ChangeDetectionStrategy.OnPush,
})
export class LinkedInCommunityConnectionDetailComponent implements OnInit {
  // Injects
  private router = inject(Router);
  private route = inject(ActivatedRoute);
  private linkedInCommunityConnectionService = inject(
    LinkedInCommunityConnectionService,
  );
  private changeDetectorRef = inject(ChangeDetectorRef);

  // State
  protected linkedInCommunityConnection?: LinkedInCommunityConnection;
  protected metadata?: LinkedInCommunityConnectionMetadata;
  protected isLoading = false;
  private routeParams: RouteParams = {};

  public ngOnInit(): void {
    this.readRouteParameters();
  }

  protected readRouteParameters(): void {
    this.route.params.subscribe((params: RouteParams) => {
      this.routeParams = params;

      if (params.id) {
        this.loadLinkedInCommunityConnection();
      }
    });
  }

  protected async loadLinkedInCommunityConnection(): Promise<void> {
    this.isLoading = true;

    try {
      const response = await lastValueFrom(
        this.linkedInCommunityConnectionService.show(
          this.routeParams.id as number,
        ),
      );
      this.linkedInCommunityConnection = response.data;
      this.metadata = response.metadata;
    } catch (error) {
      await this.router.navigateByUrl(
        `/settings/connections/linkedin-community`,
      );
      return;
    }

    this.isLoading = false;
    this.changeDetectorRef.detectChanges();
  }
}
