import {
  Component,
  DestroyRef,
  inject,
  Input,
  OnInit,
  signal,
} from '@angular/core';
import {
  FormControl,
  FormGroup,
  ReactiveFormsModule,
  Validators,
} from '@angular/forms';
import { BusinessUnit } from '@api/interfaces/dashboard/business-unit.interface';
import { BusinessUnitService } from '@api/services/dashboard/business-unit.service';
import { ToastService } from '@components/toast/toast.service';
import { TranslocoDirective, TranslocoService } from '@jsverse/transloco';
import { catchError, tap } from 'rxjs';
import { takeUntilDestroyed } from '@angular/core/rxjs-interop';
import { LoaderComponent } from '@components/loader/loader.component';
import { Router, RouterLink } from '@angular/router';
import { BusinessUnitRequest } from '@api/requests/dashboard/business-unit.request';

interface Form {
  name: FormControl<string | null>;
}

@Component({
  selector: 'app-detail',
  standalone: true,
  imports: [
    TranslocoDirective,
    LoaderComponent,
    ReactiveFormsModule,
    RouterLink,
  ],
  templateUrl: './detail.component.html',
})
export class DetailComponent implements OnInit {
  @Input({ alias: 'id' }) id!: string;
  public loading = signal<boolean>(false);
  public saving = signal<boolean>(false);
  public businessUnit = signal<BusinessUnit | null>(null);

  public form: FormGroup<Form> = new FormGroup<Form>({
    name: new FormControl(null, [Validators.required]),
  });

  private businessUnitService = inject(BusinessUnitService);
  private destroyRef = inject(DestroyRef);
  private toastService = inject(ToastService);
  private translocoService = inject(TranslocoService);
  private router = inject(Router);

  public ngOnInit(): void {
    this.loadBusinessUnit();
  }

  public save(): void {
    if (this.saving()) {
      return;
    }

    const body: BusinessUnitRequest = {
      name: this.form.controls.name.value as string,
    };

    let service = this.businessUnitService.store(body);

    const unit = this.businessUnit();

    if (unit) {
      service = this.businessUnitService.update(unit, body);
    }

    this.saving.set(true);

    service
      .pipe(
        tap(() => {
          this.router.navigate(['settings', 'data-source', 'business-units']);
          this.saving.set(false);
        }),
        catchError((err) => {
          this.toastService.addToast({
            title: this.translocoService.translate(
              'general.toasts.error.title',
            ),
            message: this.translocoService.translate(
              'general.toasts.error.description',
            ),
            icon: 'error',
            duration: 5000,
          });
          this.saving.set(false);
          return err;
        }),
        takeUntilDestroyed(this.destroyRef),
      )
      .subscribe();
  }

  private loadBusinessUnit(): void {
    if (!this.id || this.id === 'add') {
      return;
    }

    this.loading.set(true);

    this.businessUnitService
      .show(Number(this.id))
      .pipe(
        tap((response) => {
          this.businessUnit.set(response.data);
          this.loading.set(false);

          this.form.patchValue({
            name: response.data.name,
          });
        }),
        catchError((err) => {
          this.toastService.addToast({
            title: this.translocoService.translate(
              'general.toasts.error.title',
            ),
            message: this.translocoService.translate(
              'general.toasts.error.description',
            ),
            icon: 'error',
            duration: 5000,
          });
          this.loading.set(false);
          return err;
        }),
        takeUntilDestroyed(this.destroyRef),
      )
      .subscribe();
  }
}
