import { Component, DestroyRef, inject, OnInit, signal } from '@angular/core';
import { BusinessUnit } from '@api/interfaces/dashboard/business-unit.interface';
import { BusinessUnitService } from '@api/services/dashboard/business-unit.service';
import { ToastService } from '@components/toast/toast.service';
import { catchError, filter, tap } from 'rxjs';
import { TranslocoDirective, TranslocoService } from '@jsverse/transloco';
import { takeUntilDestroyed } from '@angular/core/rxjs-interop';
import { LoaderComponent } from '@components/loader/loader.component';
import { PaginatedResponse } from '@responses/paginated.response';
import { RouterLink } from '@angular/router';
import { AuthService } from '@services/auth.service';

@Component({
  selector: 'app-index',
  standalone: true,
  imports: [LoaderComponent, TranslocoDirective, RouterLink],
  templateUrl: './index.component.html',
})
export class IndexComponent implements OnInit {
  public response = signal<PaginatedResponse<BusinessUnit> | null>(null);
  public loading = signal<boolean>(false);

  private businessUnitService = inject(BusinessUnitService);
  private destroyRef = inject(DestroyRef);
  private toastService = inject(ToastService);
  private translocoService = inject(TranslocoService);
  public authService = inject(AuthService);

  public ngOnInit(): void {
    this.loadBusinessUnits();
  }

  public loadBusinessUnits(page: number = 1): void {
    if (this.loading()) {
      return;
    }

    this.loading.set(true);

    this.businessUnitService
      .index({ page })
      .pipe(
        // @ts-ignore
        filter((response): response is PaginatedResponse<BusinessUnit> => true),
        tap((response) => {
          this.response.set(response);
          this.loading.set(false);
        }),
        catchError((err) => {
          this.toastService.addToast({
            title: this.translocoService.translate(
              'general.toasts.error.title',
            ),
            message: this.translocoService.translate(
              'general.toasts.error.description',
            ),
            icon: 'error',
            duration: 5000,
          });
          return err;
        }),
        takeUntilDestroyed(this.destroyRef),
      )
      .subscribe();
  }
}
