<div *transloco="let t; prefix: 'pages.settings.data-source.sources.index'" class="overflow-hidden rounded-lg bg-lkq-pure-white shadow">
  <div class="p-6">
    <div class="border-b border-lkq-gray-200 pb-5 sm:flex sm:items-center sm:justify-between">
      <h3 class="text-base font-medium leading-6 text-lkq-gray-900">{{ t('title') }}</h3>
      <div class="mt-3 flex sm:ml-4 sm:mt-0">
        <button routerLink="/settings/data-source/sources/add" type="button" class="ml-3 inline-flex items-center rounded-md bg-lkq-electric-blue px-3 py-2 text-sm font-medium text-lkq-pure-white shadow-sm hover:bg-lkq-blue-800 focus-visible:outline focus-visible:outline-2 focus-visible:outline-offset-2 focus-visible:bg-lkq-blue-800">
          {{ t('add') }}
        </button>
      </div>
    </div>

    <div class="space-y-4 pt-4">
      <div class="flex space-x-4 items-center">
        <div>
          <p class="font-medium pl-0.5">{{ 'filters.business_unit' | transloco }}</p>
          <nz-select
            class="w-full md:w-28 disable-typing"
            nzMode="tags"
            [nzDropdownMatchSelectWidth]="false"
            [nzAllowClear]="true"
            [nzShowSearch]="false"
            [nzMaxTagCount]="0"
            [nzMaxTagPlaceholder]="tagPlaceHolder"
            [nzPlaceHolder]="'filters.placeholder' | transloco"
            [(ngModel)]="selectedBusinessUnits"
            (ngModelChange)="filterChanged()"
          >
            @for(business_unit of businessUnits(); track business_unit) {
              <nz-option [nzLabel]="business_unit.name" [nzValue]="business_unit.name"></nz-option>
            }
          </nz-select>
        </div>

        <div>
          <div class="font-medium pl-0.5">{{ 'filters.region' | transloco }}</div>
          <nz-select
            class="w-full md:w-28 ng-zorro-disable-typing"
            nzMode="tags"
            [nzDropdownMatchSelectWidth]="false"
            [nzAllowClear]="true"
            [nzShowSearch]="false"
            [nzMaxTagCount]="0"
            [nzMaxTagPlaceholder]="tagPlaceHolder"
            [nzPlaceHolder]="'filters.placeholder' | transloco"
            [(ngModel)]="selectedRegions"
            (ngModelChange)="filterChanged()"
          >
            @for(region of regions(); track region) {
              <nz-option [nzLabel]="region.name" [nzValue]="region.name"></nz-option>
            }
          </nz-select>
        </div>

        <div>
          <div class="font-medium pl-0.5">{{ 'filters.dashboards' | transloco }}</div>
          <nz-select
            class="w-full md:w-28 ng-zorro-disable-typing"
            nzMode="tags"
            [nzDropdownMatchSelectWidth]="false"
            [nzAllowClear]="true"
            [nzShowSearch]="false"
            [nzMaxTagCount]="0"
            [nzMaxTagPlaceholder]="tagPlaceHolder"
            [nzPlaceHolder]="'filters.placeholder' | transloco"
            [(ngModel)]="selectedDashboards"
            (ngModelChange)="filterChanged()"
          >
            @for(dashboard of dashboards(); track dashboard) {
              <nz-option [nzLabel]="dashboard.title" [nzValue]="dashboard.id"></nz-option>
            }
          </nz-select>
        </div>
      </div>

      @for(entry of sourceStates() | keyvalue; track  $index) {
        @if(entry.value; as state) {
        <div
          [class.pb-4]="state.open"
          class="border border-lkq-gray-200 rounded space-y-4"
        >
            <div
              (click)="toggleOpen(entry.key); loadDataSourceByType(entry.key)"
              [class.pb-4]="!state.open"
              class="flex items-center justify-between cursor-pointer px-4 pt-4">
              <h2>{{ 'enums.data-source-type.' + entry.key | transloco}}</h2>
              <button [class.rotate-180]="state.open" class="transition ease-in-out">
                <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 640 640" class="h-4 w-4"><!--!Font Awesome Free v7.0.0 by @fontawesome - https://fontawesome.com License - https://fontawesome.com/license/free Copyright 2025 Fonticons, Inc.--><path d="M297.4 470.6C309.9 483.1 330.2 483.1 342.7 470.6L534.7 278.6C547.2 266.1 547.2 245.8 534.7 233.3C522.2 220.8 501.9 220.8 489.4 233.3L320 402.7L150.6 233.4C138.1 220.9 117.8 220.9 105.3 233.4C92.8 245.9 92.8 266.2 105.3 278.7L297.3 470.7z"/></svg>
              </button>
            </div>


            @if(state.open) {
              <div class="px-4">
                <div class="bg-lkq-gray-200 h-px w-full"></div>

                <div class="flex justify-end pt-2">
                  <input
                    [formControl]="state.search"
                    type="text"
                    placeholder="{{ 'general.search' | transloco }}"
                    class="block w-64 rounded-md border-0 py-1.5 text-lkq-gray-900 shadow-sm ring-1 ring-inset ring-lkq-gray-200 placeholder:text-lkq-gray-500 focus:ring-2 focus:ring-inset focus:ring-lkq-electric-blue sm:text-sm sm:leading-6"
                  >
                </div>
              </div>
              @if(state.loading) {
                <div class="p-4 flex justify-center">
                  <component-loader></component-loader>
                </div>
              } @else if(state.response && state.response.data && state.response.meta) {
                <div class="px-4">

                  <div class="divide-y divide-lkq-gray-100">
                    @for(source of state.response.data; track $index) {
                      <div class="py-2 flex justify-between items-center">
                        <div>
                          <div class="flex space-x-2 items-center">
                            <h2 class="font-medium text-lkq-gray-900">{{ source.title }}</h2>
                            <p class="text-xs text-gray-500 space-x-1">
                              <span>{{ source.business_unit }}</span>
                              <span>-</span>
                              <span>{{ source.region}}</span>
                            </p>
                          </div>
                          <div class="flex space-x-1 items-center text-gray-500 text-xs">
                            @for(dashboard of source.dashboards ?? []; track $index; let last = $last) {
                              <p>{{ dashboard.title }}</p>
                              @if(!last) {
                                <p class="font-medium">|</p>
                              }
                            }
                          </div>
                        </div>
                        <div>
                          @if(isAllowedType(entry.key)) {
                            <span [routerLink]="'/settings/data-source/sources/' + source.id" class="cursor-pointer text-lkq-electric-blue hover:text-lkq-gray-800">{{ t('details') }}</span>
                          }
                        </div>
                      </div>
                    }
                  </div>

                  <nav *transloco="let t; prefix: 'pagination'" class="flex items-center justify-between border-t border-gray-200 px-4 sm:px-0">
                    <div class="-mt-px flex w-0 flex-1">
                      @if (state.response.meta.current_page > 1) {
                        <a (click)="loadDataSourceByType(entry.key, (state.response?.meta?.current_page ?? 1) - 1)" class="inline-flex items-center border-t-2 border-transparent pr-1 pt-4 text-sm font-medium text-lkq-gray-800 hover:border-lkq-gray-200 hover:text-lkq-deep-black hover:underline">
                          <svg class="mr-3 h-5 w-5 text-lkq-gray-800" viewBox="0 0 20 20" fill="currentColor" aria-hidden="true">
                            <path fill-rule="evenodd" d="M18 10a.75.75 0 01-.75.75H4.66l2.1 1.95a.75.75 0 11-1.02 1.1l-3.5-3.25a.75.75 0 010-1.1l3.5-3.25a.75.75 0 111.02 1.1l-2.1 1.95h12.59A.75.75 0 0118 10z" clip-rule="evenodd" />
                          </svg>
                          {{ t('previous') }}
                        </a>
                      }
                    </div>
                    <div class="hidden sm:block">
                      <p class="text-sm text-lkq-gray-800 pt-4">
                        {{ t('shows') }}
                        <span class="font-medium">{{ state.response.meta.from }}</span>
                        {{ t('until') }}
                        <span class="font-medium">{{ state.response.meta.to }}</span>
                        {{ t('of') }}
                        <span class="font-medium">{{ state.response.meta.total }}</span>
                        {{ t('results') }}
                      </p>
                    </div>
                    <div class="-mt-px flex w-0 flex-1 justify-end">
                      @if (state.response.meta.current_page < state.response.meta.last_page) {
                        <a (click)="loadDataSourceByType(entry.key, (state.response?.meta?.current_page ?? 1) + 1)" class="inline-flex items-center border-t-2 border-transparent pl-1 pt-4 text-sm font-medium text-lkq-gray-800 hover:border-lkq-gray-200 hover:text-lkq-deep-black hover:underline">
                          {{ t('next') }}
                          <svg class="ml-3 h-5 w-5 text-lkq-gray-800" viewBox="0 0 20 20" fill="currentColor" aria-hidden="true">
                            <path fill-rule="evenodd" d="M2 10a.75.75 0 01.75-.75h12.59l-2.1-1.95a.75.75 0 111.02-1.1l3.5 3.25a.75.75 0 010 1.1l-3.5 3.25a.75.75 0 11-1.02-1.1l2.1-1.95H2.75A.75.75 0 012 10z" clip-rule="evenodd" />
                          </svg>
                        </a>
                      }
                    </div>
                  </nav>
                </div>

              } @else {
              }
            }
          </div>
        }
      }
    </div>
  </div>
</div>


<ng-template #tagPlaceHolder let-selectedList>{{  'filters.value' | transloco: { count: selectedList.length} }}</ng-template>
