<div *transloco="let t; prefix: 'pages.settings.data-source.sources.detail'" class="overflow-hidden rounded-lg bg-lkq-pure-white shadow">
  <div class="p-6">

    <div class="border-b border-lkq-gray-200 pb-5 sm:flex sm:items-center sm:justify-between">
      <div>
        <h3 class="text-base font-medium leading-6 text-lkq-gray-900">{{ dataSource() ? t('edit.title') : t('add.title') }}</h3>
        <p class="mt-1 text-sm leading-6 text-lkq-gray-800">{{ dataSource() ? t('edit.description') : t('add.description') }}</p>
      </div>
      <div class="mt-3 flex sm:ml-4 sm:mt-0 items-center">
        <span routerLink="/settings/data-source/sources" class="select-none cursor-pointer text-lkq-gray-800 hover:text-lkq-electric-blue">{{ t('cancel') }}</span>
        <button
          (click)="submit()"
          [ngClass]="{
            'bg-lkq-gray-500 cursor-not-allowed hover:bg-lkq-gray-500': form.invalid || saving()
          }"
          type="button"
          class="ml-3 inline-flex items-center rounded-md bg-lkq-electric-blue px-3 py-2 text-sm font-medium text-lkq-pure-white shadow-sm hover:bg-lkq-blue-800 focus-visible:outline focus-visible:outline-2 focus-visible:outline-offset-2 focus-visible:bg-lkq-blue-800"
        >
          @if (saving()) {
            <component-loader [size]="20" class="fill-lkq-pure-white"></component-loader>
          } @else {
            {{ t('save') }}
          }
        </button>
      </div>
    </div>

    <form class="my-4 relative">
      @if (loading()) {
        <div class="overlay absolute inset-0 flex items-center justify-center">
          <component-loader></component-loader>
        </div>
      } @else {
        <div class="mt-2 grid grid-cols-1 gap-x-6 gap-y-6 sm:grid-cols-6">
          <div class="col-span-full">
            <label for="role" class="block text-sm font-medium leading-6 text-lkq-gray-900">{{ t('form.type.label') }}</label>
            <select
              *transloco="let t; prefix: 'enums.data-source-type'"
              [formControl]="form.controls.type"
              id="role"
              name="role"
              class="mt-2 block w-full rounded-md border-0 py-1.5 pl-3 pr-10 text-lkq-gray-900 ring-1 ring-inset ring-lkq-gray-200 focus:ring-2 focus:ring-lkq-electric-blue sm:text-sm sm:leading-6"
            >
              <option [ngValue]="null" disabled>{{ 'general.select' | transloco }}</option>
              @for(type of allowedTypes(); track  $index) {
                <option [ngValue]="type">{{ t(type) }}</option>
              }
            </select>
          </div>

          @if(dataSource()) {
            <p class="block text-sm font-medium leading-6 text-lkq-gray-900">{{ t('form.source.label') }}</p>
            @if(form.controls.sources.controls.at(0)?.controls; as controls) {
              <div class="col-span-full flex items-center space-x-4">
                <div class="flex justify-between w-full">
                  <div class="flex items-center space-x-1">
                    <p class="font-medium text-lkq-gray-800">{{ controls.source?.value?.name }}</p>
                    <p class="text-sm text-gray-500">({{ controls.source?.value?.external_id }})</p>
                  </div>
                  @if(controls.enabled?.value) {
                    <div>
                      <input
                        [formControl]="controls.title"
                        type="text"
                        name="email"
                        id="email"
                        placeholder="{{ t('form.title.placeholder') }}"
                        class="block w-full rounded-md border-0 py-1.5 text-lkq-gray-900 shadow-sm ring-1 ring-inset ring-lkq-gray-200 placeholder:text-lkq-gray-500 focus:ring-2 focus:ring-inset focus:ring-lkq-electric-blue sm:text-sm sm:leading-6"
                      >
                    </div>
                  }
                </div>
              </div>
            }
          } @else {
            @if(response(); as response) {
              <div class="flex items-center justify-between w-full col-span-full">
                <p class="block text-sm font-medium leading-6 text-lkq-gray-900">{{ t('form.sources.label') }}</p>
                <input
                  [formControl]="search"
                  type="text"
                  placeholder="{{ 'general.search' | transloco }}"
                  class="block w-64 rounded-md border-0 py-1.5 text-lkq-gray-900 shadow-sm ring-1 ring-inset ring-lkq-gray-200 placeholder:text-lkq-gray-500 focus:ring-2 focus:ring-inset focus:ring-lkq-electric-blue sm:text-sm sm:leading-6"
                >
              </div>

              @for (group of form.controls.sources.controls; track $index) {
                @if(isInResponse(group.controls.source.value)) {
                  <div class="col-span-full flex items-center space-x-4">
                    <input [id]="'source-' + group.controls.source.value?.id" type="checkbox" [formControl]="group.controls.enabled" class="hidden">
                    @if(group.controls.enabled.value) {
                      <label [for]="'source-' + group.controls.source.value?.id" type="button" class="ml-3 inline-flex items-center rounded-md bg-red-500 px-3 py-2 text-sm font-medium text-lkq-pure-white shadow-sm hover:bg-red-800 focus-visible:outline focus-visible:outline-2 focus-visible:outline-offset-2 focus-visible:bg-red-800">
                        {{ t('buttons.remove') }}
                      </label>
                    } @else {
                      <label [for]="'source-' + group.controls.source.value?.id" type="button" class="ml-3 inline-flex items-center rounded-md bg-lkq-electric-blue px-3 py-2 text-sm font-medium text-lkq-pure-white shadow-sm hover:bg-lkq-blue-800 focus-visible:outline focus-visible:outline-2 focus-visible:outline-offset-2 focus-visible:bg-lkq-blue-800">
                        {{ t('buttons.add') }}
                      </label>
                    }
                    <div class="flex justify-between w-full">
                      <div class="flex items-center space-x-1">
                        <p class="font-medium text-lkq-gray-800">{{ group.controls.source.value?.name }}</p>
                        <p class="text-sm text-gray-500">({{ group.controls.source.value?.external_id }})</p>
                      </div>
                      @if(group.controls.enabled.value) {
                          <div>
                            <input
                              [formControl]="group.controls.title"
                              type="text"
                              placeholder="{{ t('form.title.placeholder') }}"
                              class="block w-full rounded-md border-0 py-1.5 text-lkq-gray-900 shadow-sm ring-1 ring-inset ring-lkq-gray-200 placeholder:text-lkq-gray-500 focus:ring-2 focus:ring-inset focus:ring-lkq-electric-blue sm:text-sm sm:leading-6"
                            >
                          </div>
                      }
                    </div>
                  </div>
                }
              }

              <nav *transloco="let t; prefix: 'pagination'"
                   class="flex items-center justify-between border-t border-gray-200 px-4 sm:px-0 col-span-full">
                <div class="-mt-px flex w-0 flex-1">
                  @if (response.meta.current_page > 1) {
                    <a (click)="loadSources(response.meta.current_page - 1)"
                       class="inline-flex items-center border-t-2 border-transparent pr-1 pt-4 text-sm font-medium text-lkq-gray-800 hover:border-lkq-gray-200 hover:text-lkq-deep-black hover:underline">
                      <svg class="mr-3 h-5 w-5 text-lkq-gray-800" viewBox="0 0 20 20" fill="currentColor"
                           aria-hidden="true">
                        <path fill-rule="evenodd"
                              d="M18 10a.75.75 0 01-.75.75H4.66l2.1 1.95a.75.75 0 11-1.02 1.1l-3.5-3.25a.75.75 0 010-1.1l3.5-3.25a.75.75 0 111.02 1.1l-2.1 1.95h12.59A.75.75 0 0118 10z"
                              clip-rule="evenodd"/>
                      </svg>
                      {{ t('previous') }}
                    </a>
                  }
                </div>
                <div class="hidden sm:block">
                  <p class="text-sm text-lkq-gray-800 pt-4">
                    {{ t('shows') }}
                    <span class="font-medium">{{ response.meta.from }}</span>
                    {{ t('until') }}
                    <span class="font-medium">{{ response.meta.to }}</span>
                    {{ t('of') }}
                    <span class="font-medium">{{ response.meta.total }}</span>
                    {{ t('results') }}
                  </p>
                </div>
                <div class="-mt-px flex w-0 flex-1 justify-end">
                  @if (response.meta.current_page < response.meta.last_page) {
                    <a (click)="loadSources(response.meta.current_page + 1)"
                       class="inline-flex items-center border-t-2 border-transparent pl-1 pt-4 text-sm font-medium text-lkq-gray-800 hover:border-lkq-gray-200 hover:text-lkq-deep-black hover:underline">
                      {{ t('next') }}
                      <svg class="ml-3 h-5 w-5 text-lkq-gray-800" viewBox="0 0 20 20" fill="currentColor"
                           aria-hidden="true">
                        <path fill-rule="evenodd"
                              d="M2 10a.75.75 0 01.75-.75h12.59l-2.1-1.95a.75.75 0 111.02-1.1l3.5 3.25a.75.75 0 010 1.1l-3.5 3.25a.75.75 0 11-1.02-1.1l2.1-1.95H2.75A.75.75 0 012 10z"
                              clip-rule="evenodd"/>
                      </svg>
                    </a>
                  }
                </div>
              </nav>
            } @else if (sourcesLoading()) {
              <div class="p-4 flex justify-center w-full items-center col-span-full">
                <component-loader></component-loader>
              </div>
            }
          }

          @if(form.controls.sources.length > 0){
            <div class="col-span-full">
              <label for="region" class="block text-sm font-medium leading-6 text-lkq-gray-900">{{ t('form.region.label') }}</label>
              <select
                [formControl]="form.controls.region"
                id="region"
                name="region"
                class="mt-2 block w-full rounded-md border-0 py-1.5 pl-3 pr-10 text-lkq-gray-900 ring-1 ring-inset ring-lkq-gray-200 focus:ring-2 focus:ring-lkq-electric-blue sm:text-sm sm:leading-6"
              >
                <option [ngValue]="null" disabled>{{ 'general.select' | transloco }}</option>
                @for(region of regions(); track  $index) {
                  <option [ngValue]="region.name">{{ region.name }}</option>
                }
              </select>
            </div>

            <div class="col-span-full">
              <label for="business_unit" class="block text-sm font-medium leading-6 text-lkq-gray-900">{{ t('form.business-unit.label') }}</label>
              <select
                [formControl]="form.controls.business_unit"
                id="business_unit"
                name="business_unit"
                class="mt-2 block w-full rounded-md border-0 py-1.5 pl-3 pr-10 text-lkq-gray-900 ring-1 ring-inset ring-lkq-gray-200 focus:ring-2 focus:ring-lkq-electric-blue sm:text-sm sm:leading-6"
              >
                <option [ngValue]="null" disabled>{{ 'general.select' | transloco }}</option>
                @for(business_unit of businessUnits(); track  $index) {
                  <option [ngValue]="business_unit.name">{{ business_unit.name }}</option>
                }
              </select>
            </div>

            @if (form.controls.dashboards.length > 0) {
              <div class="col-span-full">
                <div class="flex justify-between items-center">
                  <label class="block text-sm font-medium leading-6 text-lkq-gray-900">{{ t('form.dashboards.label') }}</label>

                  <button (click)="toggleAllDashboards()" type="button">
                    @if(allDashboardsChecked()) {
                      <span>{{ t('toggle.deselect') }}</span>
                    } @else {
                      <span>{{ t('toggle.select') }}</span>
                    }
                  </button>
                </div>

                <div class="mt-2 space-y-2 grid grid-cols-2">
                  @for (checkbox of form.controls.dashboards.controls; track checkbox.controls.id.value) {
                    <div class="relative flex items-start">
                      <div class="flex h-6 items-center">
                        <input [id]="'dashboard-' + checkbox.controls.id.value" [formControl]="checkbox.controls.checked" type="checkbox" class="h-4 w-4 rounded border-lkq-gray-500 focus:ring-lkq-electric-blue">
                      </div>
                      <div class="ml-3 text-sm leading-6">
                        <label [for]="'dashboard-' + checkbox.controls.id.value" class="text-gray-900">{{ checkbox.controls.label.value }}</label>
                      </div>
                    </div>
                  }
                </div>
              </div>
            }
          }

        </div>
      }
    </form>

  </div>
</div>
