import { Component, DestroyRef, inject, OnInit, signal } from '@angular/core';
import {
  TranslocoDirective,
  TranslocoPipe,
  TranslocoService,
} from '@jsverse/transloco';
import { DataSourceType } from '@api/enums/dashboard/data-source-type.enum';
import { KeyValuePipe } from '@angular/common';
import { DataSourceService } from '@api/services/dashboard/data-source.service';
import { ToastService } from '@components/toast/toast.service';
import { catchError, debounceTime, distinctUntilChanged, tap } from 'rxjs';
import { DataSource } from '@api/interfaces/dashboard/data-source.interface';
import { PaginatedResponse } from '@responses/paginated.response';
import { takeUntilDestroyed } from '@angular/core/rxjs-interop';
import { LoaderComponent } from '@components/loader/loader.component';
import { Region } from '@api/interfaces/dashboard/region.interface';
import { RegionService } from '@api/services/dashboard/region.service';
import { BusinessUnit } from '@api/interfaces/dashboard/business-unit.interface';
import { BusinessUnitService } from '@api/services/dashboard/business-unit.service';
import { NzOptionComponent, NzSelectComponent } from 'ng-zorro-antd/select';
import { FormControl, FormsModule, ReactiveFormsModule } from '@angular/forms';
import { Dashboard } from '@api/interfaces/dashboard/dashboard.interface';
import { DashboardService } from '@api/services/dashboard/dashboard.service';
import { RouterLink } from '@angular/router';

interface SourceState {
  open: boolean;
  loading: boolean;
  search: FormControl<string | null>;
  response: PaginatedResponse<DataSource> | null;
}

@Component({
  selector: 'app-index',
  standalone: true,
  imports: [
    TranslocoDirective,
    KeyValuePipe,
    TranslocoPipe,
    LoaderComponent,
    NzOptionComponent,
    NzSelectComponent,
    FormsModule,
    RouterLink,
    ReactiveFormsModule,
  ],
  templateUrl: './index.component.html',
})
export class IndexComponent implements OnInit {
  public sourceStates = signal<{
    [key: string]: SourceState;
  }>({});
  public regions = signal<Region[]>([]);
  public businessUnits = signal<BusinessUnit[]>([]);
  public dashboards = signal<Dashboard[]>([]);
  public allowedTypes = signal<DataSourceType[]>([]);

  public selectedBusinessUnits: string[] | null = null;
  public selectedRegions: string[] | null = null;
  public selectedDashboards: number[] | null = null;

  public readonly dataSourceType = DataSourceType;

  private dataSourceService = inject(DataSourceService);
  private translocoService = inject(TranslocoService);
  private toastService = inject(ToastService);
  private destroyRef = inject(DestroyRef);
  private regionService = inject(RegionService);
  private businessUnitService = inject(BusinessUnitService);
  private dashboardService = inject(DashboardService);

  public ngOnInit(): void {
    this.setAllowedTypes();
    this.loadRegions();
    this.loadBusinessUnits();
    this.loadDashboards();

    const states: { [key: string]: SourceState } = {};

    Object.values(DataSourceType).forEach((type) => {
      states[type] = {
        open: false,
        loading: false,
        response: null,
        search: new FormControl(null),
      };

      this.listenToSearchByType(type, states[type].search);
    });

    this.sourceStates.set(states);
  }

  public loadDataSourceByType(type: string, page: number = 1): void {
    const state = this.sourceStates()[type];

    if (state.loading) {
      return;
    }

    state.loading = true;

    this.updateState(type, state);

    this.dataSourceService
      .index({
        type: type as DataSourceType,
        page,
        business_units: this.selectedBusinessUnits,
        regions: this.selectedRegions,
        dashboards: this.selectedDashboards,
        search: state.search.value,
      })
      .pipe(
        tap((response) => {
          state.response = response;
          state.loading = false;
          this.updateState(type, state);
        }),
        catchError((err) => {
          this.toastService.addToast({
            title: this.translocoService.translate(
              'general.toasts.error.title',
            ),
            message: this.translocoService.translate(
              'general.toasts.error.description',
            ),
            icon: 'error',
            duration: 5000,
          });
          state.loading = false;
          this.updateState(type, state);
          return err;
        }),
        takeUntilDestroyed(this.destroyRef),
      )
      .subscribe();
  }

  public toggleOpen(type: string): void {
    const state = this.sourceStates()[type];

    state.open = !state.open;

    this.updateState(type, state);
  }

  public filterChanged(): void {
    const states = this.sourceStates();

    const typesToReload: string[] = [];

    Object.keys(states).forEach((type: string) => {
      if (!states[type].open) {
        return;
      }

      typesToReload.push(type);
    });

    typesToReload.forEach((type) => {
      this.loadDataSourceByType(type);
    });
  }

  public isAllowedType(type: string): boolean {
    return this.allowedTypes().includes(type as DataSourceType);
  }

  private updateState(type: string, state: SourceState): void {
    this.sourceStates.update((old) => ({
      ...old,
      [type]: state,
    }));
  }

  private loadRegions(): void {
    this.regionService
      .index({})
      .pipe(
        tap((response) => {
          this.regions.set(response.data);
        }),
        takeUntilDestroyed(this.destroyRef),
      )
      .subscribe();
  }

  private loadBusinessUnits(): void {
    this.businessUnitService
      .index({})
      .pipe(
        tap((response) => {
          this.businessUnits.set(response.data);
        }),
        takeUntilDestroyed(this.destroyRef),
      )
      .subscribe();
  }

  private loadDashboards(): void {
    this.dashboardService
      .index({})
      .pipe(
        tap((response) => {
          this.dashboards.set(response.data);
        }),
        takeUntilDestroyed(this.destroyRef),
      )
      .subscribe();
  }

  private listenToSearchByType(
    type: string,
    control: FormControl<string | null>,
  ): void {
    control.valueChanges
      .pipe(
        distinctUntilChanged(),
        debounceTime(300),
        tap(() => {
          this.loadDataSourceByType(type);
        }),
        takeUntilDestroyed(this.destroyRef),
      )
      .subscribe();
  }

  private setAllowedTypes(): void {
    this.dataSourceService
      .allowedTypes()
      .pipe(
        tap((response) => {
          this.allowedTypes.set(response.data.types);
        }),
        takeUntilDestroyed(this.destroyRef),
      )
      .subscribe();
  }
}
