import {
  Component,
  DestroyRef,
  inject,
  Input,
  OnInit,
  signal,
} from '@angular/core';
import {
  FormArray,
  FormControl,
  FormGroup,
  ReactiveFormsModule,
  Validators,
} from '@angular/forms';
import { DataSourceType } from '@api/enums/dashboard/data-source-type.enum';
import { DataSource } from '@api/interfaces/dashboard/data-source.interface';
import { PaginatedResponse } from '@responses/paginated.response';
import { DataSourceSource } from '@api/interfaces/dashboard/data-source-source.interface';
import { catchError, debounceTime, distinctUntilChanged, tap } from 'rxjs';
import { takeUntilDestroyed } from '@angular/core/rxjs-interop';
import { DataSourceService } from '@api/services/dashboard/data-source.service';
import {
  TranslocoDirective,
  TranslocoPipe,
  TranslocoService,
} from '@jsverse/transloco';
import { ToastService } from '@components/toast/toast.service';
import { LoaderComponent } from '@components/loader/loader.component';
import { Region } from '@api/interfaces/dashboard/region.interface';
import { BusinessUnit } from '@api/interfaces/dashboard/business-unit.interface';
import { RegionService } from '@api/services/dashboard/region.service';
import { BusinessUnitService } from '@api/services/dashboard/business-unit.service';
import { DashboardService } from '@api/services/dashboard/dashboard.service';
import { Dashboard } from '@api/interfaces/dashboard/dashboard.interface';
import { DataSourceRequest } from '@api/requests/dashboard/data-source.request';
import { Router, RouterLink } from '@angular/router';
import { NgClass } from '@angular/common';

interface Form {
  type: FormControl<DataSourceType | null>;
  region: FormControl<string | null>;
  business_unit: FormControl<string | null>;
  dashboards: FormArray<FormGroup<FormCheckbox>>;
  sources: FormArray<FormGroup<FormSource>>;
}

interface FormCheckbox {
  id: FormControl<number>;
  label: FormControl<string>;
  checked: FormControl<boolean>;
}

interface FormSource {
  title: FormControl<string | null>;
  enabled: FormControl<boolean>;
  source: FormControl<DataSourceSource | null>;
}

@Component({
  selector: 'app-detail',
  standalone: true,
  imports: [
    TranslocoDirective,
    LoaderComponent,
    ReactiveFormsModule,
    TranslocoPipe,
    RouterLink,
    NgClass,
  ],
  templateUrl: './detail.component.html',
})
export class DetailComponent implements OnInit {
  @Input({ alias: 'id' }) id!: string;
  public dataSource = signal<DataSource | null>(null);
  public response = signal<PaginatedResponse<DataSourceSource> | null>(null);
  public sourcesLoading = signal<boolean>(false);
  public loading = signal<boolean>(false);
  public allowedTypes = signal<DataSourceType[]>([]);
  public regions = signal<Region[]>([]);
  public businessUnits = signal<BusinessUnit[]>([]);
  public dashboards = signal<Dashboard[]>([]);
  public allDashboardsChecked = signal<boolean>(false);
  public saving = signal<boolean>(false);

  public form: FormGroup<Form> = new FormGroup<Form>({
    type: new FormControl(null, [Validators.required]),
    region: new FormControl(null, [Validators.required]),
    business_unit: new FormControl(null, [Validators.required]),
    dashboards: new FormArray<FormGroup<FormCheckbox>>(
      [],
      [Validators.required],
    ),
    sources: new FormArray<FormGroup<FormSource>>([], [Validators.required]),
  });
  public search: FormControl<string | null> = new FormControl(null);

  private dataSourceService = inject(DataSourceService);
  private translocoService = inject(TranslocoService);
  private toastService = inject(ToastService);
  private destroyRef = inject(DestroyRef);
  private regionService = inject(RegionService);
  private businessUnitService = inject(BusinessUnitService);
  private dashboardService = inject(DashboardService);
  private router = inject(Router);

  public ngOnInit(): void {
    this.listenToSearch();
    this.loadRegions();
    this.loadBusinessUnits();
    this.setAllowedTypes();
    this.listenToType();
    this.loadDataSource();
  }

  public loadSources(page: number = 1): void {
    if (this.sourcesLoading() || this.dataSource()) {
      return;
    }

    this.sourcesLoading.set(true);

    this.dataSourceService
      .sources(
        this.form.controls.type.value as DataSourceType,
        page,
        this.search.value,
      )
      .pipe(
        tap((response) => {
          this.response.set(response);

          response.data.forEach((source) => {
            const exists = this.form.controls.sources.controls
              .filter((group) => group.controls.source.value?.id === source.id)
              .at(0);

            if (exists) {
              return;
            }

            const form = new FormGroup<FormSource>({
              source: new FormControl(source),
              enabled: new FormControl(false, { nonNullable: true }),
              title: new FormControl(source.name, [Validators.required]),
            });

            this.form.controls.sources.push(form);
          });

          this.sourcesLoading.set(false);
        }),
        catchError((err) => {
          this.toastService.addToast({
            title: this.translocoService.translate(
              'general.toasts.error.title',
            ),
            message: this.translocoService.translate(
              'general.toasts.error.description',
            ),
            icon: 'error',
            duration: 5000,
          });

          this.sourcesLoading.set(false);
          return err;
        }),
        takeUntilDestroyed(this.destroyRef),
      )
      .subscribe();
  }

  public toggleAllDashboards(): void {
    this.form.controls.dashboards.controls.forEach((dashboard) => {
      dashboard.controls.checked.setValue(!this.allDashboardsChecked());
    });

    this.setAllDashboardsChecked();
  }

  public isInResponse(dataSource: DataSourceSource | null): boolean {
    return !!this.response()
      ?.data.filter((source) => source.id === dataSource?.id)
      .at(0);
  }

  public submit(): void {
    const dataSource = this.dataSource();

    const hasSources =
      this.form.controls.sources.controls.filter(
        (control) => !!control.value.enabled,
      ).length > 0;

    if (this.saving() || this.form.invalid || (!dataSource && !hasSources)) {
      this.form.markAllAsTouched();
      return;
    }

    const body: DataSourceRequest = {
      type: this.form.controls.type.value as DataSourceType,
      region: this.form.controls.region.value as string,
      business_unit: this.form.controls.business_unit.value as string,
      dashboard_ids: this.form.controls.dashboards.controls
        .filter((control) => control.value.checked)
        .map((control) => control.value.id) as number[],
    };

    let service = null;

    if (dataSource) {
      service = this.dataSourceService.update(dataSource, {
        ...body,
        title: this.form.controls.sources.at(0)?.controls.title.value as string,
      });
    } else {
      service = this.dataSourceService.store({
        ...body,
        sources: this.form.controls.sources.controls
          .filter((control) => control.value.enabled)
          .map((control) => ({
            title: control.value.title as string,
            id: control.value.source?.id as number,
          })),
      });
    }

    this.saving.set(true);

    service
      .pipe(
        tap(() => {
          this.toastService.addToast({
            title: this.translocoService.translate(
              'general.toasts.success.title',
            ),
            message: this.translocoService.translate(
              'general.toasts.success.description',
            ),
            icon: 'success',
            duration: 5000,
          });

          this.saving.set(false);
          this.router.navigateByUrl('/settings/data-source/sources');
        }),
        catchError((err) => {
          this.toastService.addToast({
            title: this.translocoService.translate(
              'general.toasts.error.title',
            ),
            message: this.translocoService.translate(
              'general.toasts.error.description',
            ),
            icon: 'error',
            duration: 5000,
          });
          this.saving.set(false);
          return err;
        }),
        takeUntilDestroyed(this.destroyRef),
      )
      .subscribe();
  }

  private listenToType(): void {
    this.form.controls.type.valueChanges
      .pipe(
        distinctUntilChanged(),
        tap(() => {
          this.loadSources();
        }),
        takeUntilDestroyed(this.destroyRef),
      )
      .subscribe();
  }

  private setAllowedTypes(): void {
    this.dataSourceService
      .allowedTypes()
      .pipe(
        tap((response) => {
          this.allowedTypes.set(response.data.types);
        }),
        takeUntilDestroyed(this.destroyRef),
      )
      .subscribe();
  }

  private loadRegions(): void {
    this.regionService
      .index({})
      .pipe(
        tap((response) => {
          this.regions.set(response.data);
        }),
        takeUntilDestroyed(this.destroyRef),
      )
      .subscribe();
  }

  private loadBusinessUnits(): void {
    this.businessUnitService
      .index({})
      .pipe(
        tap((response) => {
          this.businessUnits.set(response.data);
        }),
        takeUntilDestroyed(this.destroyRef),
      )
      .subscribe();
  }

  private async loadDataSource(): Promise<void> {
    const dashboards = await this.dashboardService.index({}).toPromise();

    if (dashboards) {
      this.dashboards.set(dashboards.data);
    }

    if (this.id === 'add') {
      this.setDashboardFormArray();
      return;
    }

    this.loading.set(true);

    this.dataSourceService
      .show(parseInt(this.id))
      .pipe(
        tap((response) => {
          this.dataSource.set(response.data);

          this.form.patchValue({
            type: response.data.source?.type ?? null,
            region: response.data.region,
            business_unit: response.data.business_unit,
          });

          const group = new FormGroup<FormSource>({
            title: new FormControl(response.data.title),
            source: new FormControl(response.data.source ?? null),
            enabled: new FormControl(true, { nonNullable: true }),
          });

          this.form.controls.type.disable();
          this.form.controls.sources.push(group);

          this.setDashboardFormArray();
          this.loading.set(false);
        }),
        catchError((err) => {
          this.toastService.addToast({
            title: this.translocoService.translate(
              'general.toasts.error.title',
            ),
            message: this.translocoService.translate(
              'general.toasts.error.description',
            ),
            icon: 'error',
            duration: 5000,
          });
          this.loading.set(false);
          return err;
        }),
        takeUntilDestroyed(this.destroyRef),
      )
      .subscribe();
  }

  private setDashboardFormArray(): void {
    const dashboards = this.dashboards();

    if (!dashboards) {
      return;
    }

    dashboards.forEach((dashboard) => {
      const checked =
        (
          this.dataSource()?.dashboards?.map((existing) => existing.id) ?? []
        ).indexOf(dashboard.id) !== -1;
      this.form.controls.dashboards.push(
        new FormGroup({
          id: new FormControl(dashboard.id, { nonNullable: true }),
          label: new FormControl(dashboard.title, { nonNullable: true }),
          checked: new FormControl(checked, { nonNullable: true }),
        }),
      );
    });

    this.setAllDashboardsChecked();
  }

  private setAllDashboardsChecked(): void {
    this.allDashboardsChecked.set(
      this.form.controls.dashboards.controls.filter(
        (dashboard) => dashboard.controls.checked.value,
      ).length === this.form.controls.dashboards.controls.length,
    );
  }

  private listenToSearch(): void {
    this.search.valueChanges
      .pipe(
        distinctUntilChanged(),
        debounceTime(300),
        tap(() => {
          this.loadSources();
        }),
        takeUntilDestroyed(this.destroyRef),
      )
      .subscribe();
  }
}
