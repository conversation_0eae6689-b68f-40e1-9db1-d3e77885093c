import { Component } from '@angular/core';
import { TranslocoDirective } from '@jsverse/transloco';
import { FormControl, FormGroup, Validators, ReactiveFormsModule } from '@angular/forms';
import { RouterLink } from '@angular/router';
import { CommonModule } from '@angular/common';

interface Form {
  business_unit: FormControl<string | null>;
  dashboard: FormControl<string | null>;
  multiple_items: FormControl<boolean>;
  targets: FormControl<string | null>;
  region: FormGroup<RegionForm>;
}

interface RegionForm {
  select: FormControl<string | null>;
  create: FormControl<string | null>;
}

@Component({
  selector: 'app-onboarding',
  standalone: true,
  imports: [TranslocoDirective, ReactiveFormsModule, RouterLink, CommonModule],
  templateUrl: './onboarding.component.html',
})
export class OnboardingComponent {
  public form: FormGroup<Form> = new FormGroup<Form>({
    business_unit: new FormControl(null, [Validators.required]),
    dashboard: new FormControl(null, [Validators.required]),
    multiple_items: new FormControl(false, { nonNullable: true }),
    targets: new FormControl(null),
    region: new FormGroup<RegionForm>({
      select: new FormControl(null),
      create: new FormControl(null),
    }),
  });
}
