import { Component, DestroyRef, inject, OnInit, signal } from '@angular/core';
import { PaginatedResponse } from '@responses/paginated.response';
import { ToastService } from '@components/toast/toast.service';
import { TranslocoDirective, TranslocoService } from '@jsverse/transloco';
import { AuthService } from '@services/auth.service';
import { catchError, filter, tap } from 'rxjs';
import { takeUntilDestroyed } from '@angular/core/rxjs-interop';
import { DashboardService } from '@api/services/dashboard/dashboard.service';
import { Dashboard } from '@api/interfaces/dashboard/dashboard.interface';
import { RouterLink } from '@angular/router';
import { LoaderComponent } from '@components/loader/loader.component';

@Component({
  selector: 'app-index',
  standalone: true,
  imports: [RouterLink, TranslocoDirective, LoaderComponent],
  templateUrl: './index.component.html',
})
export class IndexComponent implements OnInit {
  public response = signal<PaginatedResponse<Dashboard> | null>(null);
  public loading = signal<boolean>(false);

  private dashboardService = inject(DashboardService);
  private destroyRef = inject(DestroyRef);
  private toastService = inject(ToastService);
  private translocoService = inject(TranslocoService);
  public authService = inject(AuthService);

  public ngOnInit(): void {
    this.loadDashboards();
  }

  public loadDashboards(page: number = 1): void {
    if (this.loading()) {
      return;
    }

    this.loading.set(true);

    this.dashboardService
      .index({ page })
      .pipe(
        // @ts-ignore
        filter((response): response is PaginatedResponse<Dashboard> => true),
        tap((response) => {
          this.response.set(response);
          this.loading.set(false);
        }),
        catchError((err) => {
          this.toastService.addToast({
            title: this.translocoService.translate(
              'general.toasts.error.title',
            ),
            message: this.translocoService.translate(
              'general.toasts.error.description',
            ),
            icon: 'error',
            duration: 5000,
          });
          return err;
        }),
        takeUntilDestroyed(this.destroyRef),
      )
      .subscribe();
  }
}
