<div *transloco="let t; prefix: 'pages.settings.data-source.dashboards.detail'" class="overflow-hidden rounded-lg bg-lkq-pure-white shadow">
  <div class="p-6">

    <div class="border-b border-lkq-gray-200 pb-5 sm:flex sm:items-center sm:justify-between">
      <div>
        <h3 class="text-base font-medium leading-6 text-lkq-gray-900">{{ dashboard() ? t('edit.title', { name: dashboard()?.title }) : t('add.title') }}</h3>
        <p class="mt-1 text-sm leading-6 text-lkq-gray-800">{{ dashboard() ? t('edit.description') : t('add.description') }}</p>
      </div>
      <div class="mt-3 flex sm:ml-4 sm:mt-0 items-center">
        <span routerLink="/settings/data-source/dashboards" class="select-none cursor-pointer text-lkq-gray-800 hover:text-lkq-electric-blue">{{ t('cancel') }}</span>
        <button (click)="save()" type="button" class="ml-3 inline-flex items-center rounded-md bg-lkq-electric-blue px-3 py-2 text-sm font-medium text-lkq-pure-white shadow-sm hover:bg-lkq-blue-800 focus-visible:outline focus-visible:outline-2 focus-visible:outline-offset-2 focus-visible:bg-lkq-blue-800">
          @if (loading() || saving()) {
            <component-loader [size]="20" class="fill-lkq-pure-white"></component-loader>
          } @else {
            {{ t('save') }}
          }
        </button>
      </div>
    </div>

    <form class="my-4 relative">
      @if (loading()) {
        <div class="overlay absolute inset-0 flex items-center justify-center">
          <component-loader></component-loader>
        </div>
      } @else {
        <div class="mt-2 grid grid-cols-1 gap-x-6 gap-y-6 sm:grid-cols-6">
          <div class="col-span-full">
            <label for="name" class="block text-sm font-medium leading-6 text-lkq-gray-900">{{ t('form.title.label') }}</label>
            <div class="mt-2">
              <input
                [formControl]="form.controls.title"
                type="text"
                name="name"
                id="name"
                placeholder="{{ t('form.title.placeholder') }}"
                class="block w-full rounded-md border-0 py-1.5 text-lkq-gray-900 shadow-sm ring-1 ring-inset ring-lkq-gray-200 placeholder:text-lkq-gray-500 focus:ring-2 focus:ring-inset focus:ring-lkq-electric-blue sm:text-sm sm:leading-6"
              >
            </div>
          </div>
        </div>
      }
    </form>

  </div>
</div>
