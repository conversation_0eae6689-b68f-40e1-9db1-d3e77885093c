import { Component, DestroyRef, inject, Input, signal } from '@angular/core';
import {
  FormControl,
  FormGroup,
  ReactiveFormsModule,
  Validators,
} from '@angular/forms';
import { ToastService } from '@components/toast/toast.service';
import { TranslocoDirective, TranslocoService } from '@jsverse/transloco';
import { Router, RouterLink } from '@angular/router';
import { catchError, tap } from 'rxjs';
import { takeUntilDestroyed } from '@angular/core/rxjs-interop';
import { Dashboard } from '@api/interfaces/dashboard/dashboard.interface';
import { DashboardRequest } from '@api/requests/dashboard/dashboard.request';
import { DashboardService } from '@api/services/dashboard/dashboard.service';
import { LoaderComponent } from '@components/loader/loader.component';

interface Form {
  title: FormControl<string | null>;
}

@Component({
  selector: 'app-detail',
  standalone: true,
  imports: [
    LoaderComponent,
    RouterLink,
    TranslocoDirective,
    ReactiveFormsModule,
  ],
  templateUrl: './detail.component.html',
})
export class DetailComponent {
  @Input({ alias: 'id' }) id!: string;
  public loading = signal<boolean>(false);
  public saving = signal<boolean>(false);
  public dashboard = signal<Dashboard | null>(null);

  public form: FormGroup<Form> = new FormGroup<Form>({
    title: new FormControl(null, [Validators.required]),
  });

  private dashboardService = inject(DashboardService);
  private destroyRef = inject(DestroyRef);
  private toastService = inject(ToastService);
  private translocoService = inject(TranslocoService);
  private router = inject(Router);

  public ngOnInit(): void {
    this.loadDashboard();
  }

  public save(): void {
    if (this.saving()) {
      return;
    }

    const body: DashboardRequest = {
      title: this.form.controls.title.value as string,
    };

    let service = this.dashboardService.store(body);

    const unit = this.dashboard();

    if (unit) {
      service = this.dashboardService.update(unit, body);
    }

    this.saving.set(true);

    service
      .pipe(
        tap(() => {
          this.router.navigate(['settings', 'data-source', 'dashboards']);
          this.saving.set(false);
        }),
        catchError((err) => {
          this.toastService.addToast({
            title: this.translocoService.translate(
              'general.toasts.error.title',
            ),
            message: this.translocoService.translate(
              'general.toasts.error.description',
            ),
            icon: 'error',
            duration: 5000,
          });
          this.saving.set(false);
          return err;
        }),
        takeUntilDestroyed(this.destroyRef),
      )
      .subscribe();
  }

  private loadDashboard(): void {
    if (!this.id || this.id === 'add') {
      return;
    }

    this.loading.set(true);

    this.dashboardService
      .show(Number(this.id))
      .pipe(
        tap((response) => {
          this.dashboard.set(response.data);
          this.loading.set(false);

          this.form.patchValue({
            title: response.data.title,
          });
        }),
        catchError((err) => {
          this.toastService.addToast({
            title: this.translocoService.translate(
              'general.toasts.error.title',
            ),
            message: this.translocoService.translate(
              'general.toasts.error.description',
            ),
            icon: 'error',
            duration: 5000,
          });
          this.loading.set(false);
          return err;
        }),
        takeUntilDestroyed(this.destroyRef),
      )
      .subscribe();
  }
}
