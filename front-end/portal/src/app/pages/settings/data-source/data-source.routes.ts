import { Routes } from '@angular/router';

export const DataSourceRoutes: Routes = [
  {
    path: 'business-units',
    loadChildren: () =>
      import('./business-unit/business-unit.routes').then((r) => r.routes),
  },
  {
    path: 'regions',
    loadChildren: () => import('./region/region.routes').then((r) => r.routes),
  },
  {
    path: 'sources',
    loadChildren: () =>
      import('./sources/sources.routes').then((r) => r.routes),
  },
  {
    path: 'dashboards',
    loadChildren: () =>
      import('./dashboards/dashboards.routes').then((r) => r.routes),
  },
  {
    path: 'onboarding',
    loadComponent: () =>
      import('./onboarding/onboarding.component').then(
        (r) => r.OnboardingComponent,
      ),
  },
];
