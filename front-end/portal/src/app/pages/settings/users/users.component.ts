import {
  ChangeDetectionStrategy,
  ChangeDetectorRef,
  Component,
  inject,
  OnInit,
} from '@angular/core';
import { CommonModule } from '@angular/common';
import { TranslocoDirective } from '@jsverse/transloco';
import { PaginatedResponse } from '@responses/paginated.response';
import { User } from '@api/interfaces/authentication/user.interface';
import { LoaderComponent } from '@components/loader/loader.component';
import { lastValueFrom } from 'rxjs';
import { UserService } from '@api/services/authentication/user.service';
import { RouterLink } from '@angular/router';
import { ConfirmationModalService } from '@components/confirmation-modal/confirmation-modal.service';

@Component({
  selector: 'pages-settings-users',
  templateUrl: './users.component.html',
  standalone: true,
  imports: [CommonModule, TranslocoDirective, LoaderComponent, RouterLink],
  changeDetection: ChangeDetectionStrategy.OnPush,
})
export class UsersComponent implements OnInit {
  // State
  public isLoading = true;
  public pageNumber?: number;
  public paginatedResponse?: PaginatedResponse<User>;

  // Injects
  protected userService = inject(UserService);
  protected changeDetectorRef = inject(ChangeDetectorRef);
  protected confirmationModalService = inject(ConfirmationModalService);

  public ngOnInit(): void {
    this.loadUsers();
  }

  public async delete(user: User): Promise<void> {
    await this.confirmationModalService.confirm(
      {
        confirmed: async () => {
          await lastValueFrom(this.userService.destroy(user.id));
          await this.loadUsers(this.pageNumber);
        },
      },
      {
        title: 'pages.settings.users.index.modals.delete.title',
        description: 'pages.settings.users.index.modals.delete.description',
      },
    );
  }

  protected async loadUsers(page?: number): Promise<void> {
    this.isLoading = true;
    this.pageNumber = page;

    this.paginatedResponse = await lastValueFrom(this.userService.index(page));

    this.isLoading = false;
    this.changeDetectorRef.detectChanges();
  }
}
