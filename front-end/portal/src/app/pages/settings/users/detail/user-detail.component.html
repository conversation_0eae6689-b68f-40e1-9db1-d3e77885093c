<div *transloco="let t; prefix: 'pages.settings.users.detail'" class="overflow-hidden rounded-lg bg-lkq-pure-white shadow">
  <div class="p-6">

    <div class="border-b border-lkq-gray-200 pb-5 sm:flex sm:items-center sm:justify-between">
      <div>
        <h3 class="text-base font-medium leading-6 text-lkq-gray-900">{{ user ? t('edit.title', { name: user.name }) : t('add.title') }}</h3>
        <p class="mt-1 text-sm leading-6 text-lkq-gray-800">{{ user ? t('edit.description') : t('add.description') }}</p>
      </div>
      <div class="mt-3 flex sm:ml-4 sm:mt-0 items-center">
        <span routerLink="/settings/users" class="select-none cursor-pointer text-lkq-gray-800 hover:text-lkq-electric-blue">{{ t('cancel') }}</span>
        <button (click)="save()" type="button" class="ml-3 inline-flex items-center rounded-md bg-lkq-electric-blue px-3 py-2 text-sm font-medium text-lkq-pure-white shadow-sm hover:bg-lkq-blue-800 focus-visible:outline focus-visible:outline-2 focus-visible:outline-offset-2 focus-visible:bg-lkq-blue-800">
          @if (isLoading) {
              <component-loader [size]="20" class="fill-lkq-pure-white"></component-loader>
          } @else {
            {{ t('save') }}
          }
        </button>
      </div>
    </div>

    <form class="my-4 relative">
      @if (isLoading) {
        <div class="overlay absolute inset-0 flex items-center justify-center">
            <component-loader></component-loader>
        </div>
      }
      <div class="mt-2 grid grid-cols-1 gap-x-6 gap-y-6 sm:grid-cols-6">
        <div class="col-span-full">
          <label for="name" class="block text-sm font-medium leading-6 text-lkq-gray-900">{{ t('form.name.label') }}</label>
          <div class="mt-2">
            <input
              [formControl]="form.controls.name"
              type="text"
              name="name"
              id="name"
              placeholder="{{ t('form.name.placeholder') }}"
              class="block w-full rounded-md border-0 py-1.5 text-lkq-gray-900 shadow-sm ring-1 ring-inset ring-lkq-gray-200 placeholder:text-lkq-gray-500 focus:ring-2 focus:ring-inset focus:ring-lkq-electric-blue sm:text-sm sm:leading-6"
            >
          </div>
        </div>

        <div class="col-span-full">
          <label for="email" class="block text-sm font-medium leading-6 text-lkq-gray-900">{{ t('form.email.label') }}</label>
          <div class="mt-2">
            <input
              [formControl]="form.controls.email"
              type="text"
              name="email"
              id="email"
              placeholder="{{ t('form.email.placeholder') }}"
              class="block w-full rounded-md border-0 py-1.5 text-lkq-gray-900 shadow-sm ring-1 ring-inset ring-lkq-gray-200 placeholder:text-lkq-gray-500 focus:ring-2 focus:ring-inset focus:ring-lkq-electric-blue sm:text-sm sm:leading-6"
            >
          </div>
        </div>

        <div class="col-span-full">
          <label for="role" class="block text-sm font-medium leading-6 text-lkq-gray-900">{{ t('form.role.label') }}</label>
          <select
            *transloco="let t; prefix: 'enums.authentication.role'"
            [formControl]="form.controls.role"
            id="role"
            name="role"
            class="mt-2 block w-full rounded-md border-0 py-1.5 pl-3 pr-10 text-lkq-gray-900 ring-1 ring-inset ring-lkq-gray-200 focus:ring-2 focus:ring-lkq-electric-blue sm:text-sm sm:leading-6"
          >
            <option [ngValue]="role.USER">{{ t(role.USER) }}</option>
            <option [ngValue]="role.ADMIN">{{ t(role.ADMIN) }}</option>
            <option [ngValue]="role.SUPER_ADMIN">{{ t(role.SUPER_ADMIN) }}</option>
            @if(user?.role === role.MASTER_ADMIN) {
              <option [ngValue]="role.MASTER_ADMIN">{{ t(role.SUPER_ADMIN) }}</option>
            }
            <option [ngValue]="role.VIEW_MANAGER">{{ t(role.VIEW_MANAGER) }}</option>
          </select>
        </div>

        @if (form.controls.dashboards.length > 0) {
          <div class="col-span-full">
            <div class="flex justify-between items-center">
              <label class="block text-sm font-medium leading-6 text-lkq-gray-900">{{ t('form.dashboards.label') }}</label>

              <button (click)="toggleAllDashboards()" type="button">
                @if(allDashboardsChecked) {
                  <span>{{ t('toggle.deselect') }}</span>
                } @else {
                  <span>{{ t('toggle.select') }}</span>
                }
              </button>
            </div>

            <div class="mt-2 space-y-2 grid grid-cols-2">
              @for (checkbox of form.controls.dashboards.controls; track checkbox.controls.id.value) {
                <div class="relative flex items-start">
                  <div class="flex h-6 items-center">
                    <input [id]="'dashboard-' + checkbox.controls.id.value" [formControl]="checkbox.controls.checked" type="checkbox" class="h-4 w-4 rounded border-lkq-gray-500 focus:ring-lkq-electric-blue">
                  </div>
                  <div class="ml-3 text-sm leading-6">
                    <label [for]="'dashboard-' + checkbox.controls.id.value" class="text-gray-900">{{ checkbox.controls.label.value }}</label>
                  </div>
                </div>
              }
            </div>
          </div>
        }
      </div>
    </form>

  </div>
</div>
