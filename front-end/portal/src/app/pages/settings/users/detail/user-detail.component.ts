import {
  ChangeDetectionStrategy,
  Component,
  inject,
  OnInit,
} from '@angular/core';
import { CommonModule } from '@angular/common';
import { TranslocoDirective } from '@jsverse/transloco';
import { Role } from '@api/enums/authentication/role.enum';
import { ActivatedRoute, Router, RouterLink } from '@angular/router';
import {
  FormArray,
  FormControl,
  FormGroup,
  ReactiveFormsModule,
  Validators,
} from '@angular/forms';
import { UserService } from '@api/services/authentication/user.service';
import { LoaderComponent } from '@components/loader/loader.component';
import { lastValueFrom } from 'rxjs';
import { User } from '@api/interfaces/authentication/user.interface';
import { UserMetadata } from '@api/interfaces/authentication/user-metadata.interface';

interface RouteParams {
  id?: number | 'add';
}

interface FormCheckbox {
  id: FormControl<number>;
  label: FormControl<string>;
  checked: FormControl<boolean>;
}

interface Form {
  name: FormControl<string>;
  email: FormControl<string>;
  role: FormControl<Role>;
  dashboards: FormArray<FormGroup<FormCheckbox>>;
}

@Component({
  selector: 'pages-settings-user-detail',
  templateUrl: './user-detail.component.html',
  standalone: true,
  imports: [
    CommonModule,
    TranslocoDirective,
    ReactiveFormsModule,
    LoaderComponent,
    RouterLink,
  ],
  changeDetection: ChangeDetectionStrategy.OnPush,
})
export class UserDetailComponent implements OnInit {
  // Injects
  private router = inject(Router);
  private route = inject(ActivatedRoute);
  private userService = inject(UserService);

  // State
  public user?: User;
  public metadata?: UserMetadata;
  public isLoading = false;
  public allDashboardsChecked = false;
  private routeParams: RouteParams = {};

  // Form
  public form = new FormGroup<Form>({
    name: new FormControl('', {
      validators: [Validators.required],
      nonNullable: true,
    }),
    email: new FormControl('', {
      validators: [Validators.required, Validators.email],
      nonNullable: true,
    }),
    role: new FormControl(Role.USER, {
      validators: [Validators.required],
      nonNullable: true,
    }),
    dashboards: new FormArray<FormGroup<FormCheckbox>>([]),
  });

  // Enums
  protected readonly role = Role;

  public ngOnInit(): void {
    this.readRouteParameters();
  }

  protected readRouteParameters(): void {
    this.route.params.subscribe((params: RouteParams) => {
      this.routeParams = params;

      if (params.id && params.id !== 'add') {
        this.loadUser();
      }
    });
  }

  protected async loadUser(): Promise<void> {
    this.isLoading = true;

    try {
      const response = await lastValueFrom(
        this.userService.show(this.routeParams.id as number),
      );
      this.user = response.data;
      this.metadata = response.metadata;
    } catch (error) {
      await this.router.navigateByUrl(`/settings/users`);
      return;
    }

    this.form.patchValue({
      name: this.user.name,
      email: this.user.email,
      role: this.user.role,
    });

    if (this.user.role === Role.MASTER_ADMIN) {
      this.form.controls.role.disable();
    }

    this.setDashboardFormArray();

    this.isLoading = false;
  }

  protected setDashboardFormArray(): void {
    if (!this.metadata) {
      return;
    }

    this.metadata.dashboards?.forEach((dashboard) => {
      const checked = this.user?.dashboards.indexOf(dashboard.id) !== -1;
      this.form.controls.dashboards.push(
        new FormGroup({
          id: new FormControl(dashboard.id, { nonNullable: true }),
          label: new FormControl(dashboard.title, { nonNullable: true }),
          checked: new FormControl(checked, { nonNullable: true }),
        }),
      );
    });

    this.setAllDashboardsChecked();
  }

  public async save(): Promise<void> {
    if (this.isLoading) {
      return;
    }

    if (!this.form.valid) {
      this.form.markAllAsTouched();
      return;
    }

    this.isLoading = true;

    const checkedDashboardIds = this.form.controls.dashboards.value
      .filter((dashboard) => dashboard.checked as boolean)
      .map((dashboard) => dashboard.id as number);

    const data = {
      name: this.form.value.name,
      email: this.form.value.email,
      role: this.form.value.role,
      dashboards: checkedDashboardIds ?? [],
    };

    if (!this.user) {
      const response = await lastValueFrom(this.userService.store(data));
      this.user = response.data;
    } else {
      await lastValueFrom(this.userService.update(this.user.id, data));
    }

    await this.router.navigateByUrl(`/settings/users/${this.user.id}`);
  }

  public toggleAllDashboards(): void {
    this.form.controls.dashboards.controls.forEach((dashboard) => {
      dashboard.controls.checked.setValue(!this.allDashboardsChecked);
    });

    this.setAllDashboardsChecked();
  }

  private setAllDashboardsChecked(): void {
    this.allDashboardsChecked =
      this.form.controls.dashboards.controls.filter(
        (dashboard) => dashboard.controls.checked.value,
      ).length === this.form.controls.dashboards.controls.length;
  }
}
