<main *transloco="let t; prefix: 'pages.not_found'" class="grid min-h-full place-items-center bg-lkq-pure-white px-6 py-24 sm:py-32 lg:px-8 pt-10">
  <div class="text-center">
    <p class="text-base font-medium text-lkq-electric-blue">
      404
    </p>
    <h1 class="mt-4 text-3xl font-medium tracking-tight text-lkq-gray-900 sm:text-5xl">
      {{ t('title') }}
    </h1>
    <p class="mt-6 text-base leading-7 text-lkq-gray-800">
      {{ t('description') }}
    </p>
    <div class="mt-10 flex items-center justify-center gap-x-6">
      <!-- TODO: Add working link -->
      <a href="#" class="inline-flex items-center rounded-md bg-lkq-electric-blue px-3 py-2 text-sm font-medium text-lkq-pure-white shadow-sm hover:text-lkq-pure-white hover:bg-lkq-blue-800 focus-visible:outline focus-visible:outline-2 focus-visible:outline-offset-2 focus-visible:bg-lkq-blue-800">
        {{ t('back') }}
      </a>
    </div>
  </div>
</main>
