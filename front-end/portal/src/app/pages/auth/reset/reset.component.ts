import {
  ChangeDetectionStrategy,
  Component,
  inject,
  OnInit,
} from '@angular/core';
import { LoginService } from '@api/services/authentication/login.service';
import { lastValueFrom } from 'rxjs';
import { ActivatedRoute, Router, RouterLink } from '@angular/router';
import { LoaderComponent } from '@components/loader/loader.component';
import { TranslocoDirective, TranslocoService } from '@jsverse/transloco';
import {
  FormControl,
  FormGroup,
  FormsModule,
  ReactiveFormsModule,
  Validators,
} from '@angular/forms';
import { ToastService } from '@components/toast/toast.service';

interface QueryParams {
  email?: string;
  token?: string;
}

interface Form {
  password: FormControl<string>;
}

@Component({
  selector: 'pages-auth-reset',
  templateUrl: './reset.component.html',
  standalone: true,
  imports: [
    LoaderComponent,
    ReactiveFormsModule,
    TranslocoDirective,
    FormsModule,
    RouterLink,
  ],
  changeDetection: ChangeDetectionStrategy.OnPush,
})
export class ResetComponent implements OnInit {
  // Injects
  private loginService = inject(LoginService);
  private router = inject(Router);
  private toastService = inject(ToastService);
  private translocoService = inject(TranslocoService);
  private route = inject(ActivatedRoute);

  // State
  public isLoading = false;
  public params: QueryParams | undefined = undefined;

  // Form
  public form = new FormGroup<Form>({
    password: new FormControl('', {
      validators: [Validators.required],
      nonNullable: true,
    }),
  });

  public ngOnInit(): void {
    this.readRouteParameters();
  }

  protected readRouteParameters(): void {
    this.route.queryParams.subscribe((params: QueryParams) => {
      this.params = params;
    });
  }

  protected showResetSubmitted() {
    this.toastService.addToast({
      title: this.translocoService.translate(
        'pages.auth.toasts.reset_succeeded.title',
      ),
      message: this.translocoService.translate(
        'pages.auth.toasts.reset_succeeded.description',
      ),
      icon: 'success',
      duration: 5000,
    });
  }

  public async submit(): Promise<void> {
    if (this.isLoading) {
      return;
    }

    if (!this.form.valid) {
      this.form.markAllAsTouched();
      return;
    }

    this.isLoading = true;

    await lastValueFrom(this.loginService.csrf());

    await lastValueFrom(
      this.loginService.resetPassword(
        this.params?.email ?? '',
        this.form.controls.password.value,
        this.params?.token ?? '',
      ),
    );

    this.showResetSubmitted();

    await this.router.navigateByUrl('/auth/login');
  }
}
