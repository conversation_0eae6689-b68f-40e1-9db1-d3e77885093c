<ng-container *transloco="let t; prefix: 'pages.auth.reset'">
  @if (isLoading) {
    <div class="h-10">
      <component-loader></component-loader>
    </div>
  } @else {

    <div class="pb-4">
      <h3 class="text-base font-medium leading-6 text-lkq-gray-900">{{ t('title') }}</h3>
      <p class="mt-1 text-sm leading-6 text-lkq-gray-800">{{ t('sub_title') }}</p>
    </div>

    <form class="space-y-2" (ngSubmit)="submit()">
      <div class="col-span-full">
        <label for="email" class="block text-sm font-medium leading-6 text-lkq-gray-900">{{ t('form.email.label') }}</label>
        <div class="mt-2">
          <input
            [value]="params?.email"
            type="text"
            name="email"
            id="email"
            disabled
            class="block w-full rounded-md border-0 py-1.5 text-lkq-gray-900 shadow-sm ring-1 ring-inset ring-lkq-gray-200 placeholder:text-lkq-gray-500 focus:ring-2 focus:ring-inset focus:ring-lkq-electric-blue sm:text-sm sm:leading-6"
          >
        </div>
      </div>

      <div class="col-span-full">
        <label for="password" class="block text-sm font-medium leading-6 text-lkq-gray-900">{{ t('form.password.label') }}</label>
        <div class="mt-2">
          <input
            [formControl]="form.controls.password"
            [placeholder]="t('form.password.placeholder')"
            type="password"
            name="password"
            id="password"
            class="block w-full rounded-md border-0 py-1.5 text-lkq-gray-900 shadow-sm ring-1 ring-inset ring-lkq-gray-200 placeholder:text-lkq-gray-500 focus:ring-2 focus:ring-inset focus:ring-lkq-electric-blue sm:text-sm sm:leading-6"
          >
        </div>
      </div>

      <div class="w-full flex justify-between items-center mt-2">
        <a routerLink="/auth/login" class="inline-flex items-center border-t-2 border-transparent pl-1 text-sm font-medium text-lkq-gray-800 hover:border-lkq-gray-200 hover:text-lkq-deep-black hover:underline">
          {{ t('back_to_login') }}
        </a>
        <button type="submit" class="inline-flex items-center rounded-md bg-lkq-electric-blue px-3 py-2 text-sm font-medium text-lkq-pure-white shadow-sm hover:bg-lkq-blue-800 focus-visible:outline focus-visible:outline-2 focus-visible:outline-offset-2 focus-visible:bg-lkq-blue-800">
          @if (isLoading) {
            <component-loader [size]="20" class="fill-lkq-pure-white"></component-loader>
          } @else {
            {{ t('submit') }}
          }
        </button>
      </div>
    </form>
  }
</ng-container>
