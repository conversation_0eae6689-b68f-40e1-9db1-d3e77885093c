import {
  ChangeDetectionStrategy,
  ChangeDetectorRef,
  Component,
  inject,
  OnInit,
} from '@angular/core';
import { LoginService } from '@api/services/authentication/login.service';
import { AuthService } from '@services/auth.service';
import { lastValueFrom } from 'rxjs';
import { Router, RouterLink } from '@angular/router';
import { LoaderComponent } from '@components/loader/loader.component';
import { TranslocoDirective, TranslocoService } from '@jsverse/transloco';
import {
  FormControl,
  FormGroup,
  FormsModule,
  ReactiveFormsModule,
  Validators,
} from '@angular/forms';
import { ToastService } from '@components/toast/toast.service';
import { TwoFactorService } from '@api/services/authentication/two-factor.service';

interface Form {
  email: FormControl<string>;
  password: FormControl<string>;
  twoFactor: FormControl<string>;
}

@Component({
  selector: 'pages-auth-login',
  templateUrl: './login.component.html',
  standalone: true,
  imports: [
    LoaderComponent,
    ReactiveFormsModule,
    TranslocoDirective,
    FormsModule,
    RouterLink,
  ],
  changeDetection: ChangeDetectionStrategy.OnPush,
})
export class LoginComponent implements OnInit {
  // Injects
  private loginService = inject(LoginService);
  private twoFactorService = inject(TwoFactorService);
  private authService = inject(AuthService);
  private router = inject(Router);
  private toastService = inject(ToastService);
  private translocoService = inject(TranslocoService);
  private changeDetectorRef = inject(ChangeDetectorRef);

  // State
  public isLoading = false;
  public isWaitingForTwoFactor = false;

  // Form
  public form!: FormGroup<Form>;

  public ngOnInit(): void {
    this.initForm();
  }

  protected initForm(): void {
    this.form = new FormGroup<Form>({
      email: new FormControl('', {
        validators: [Validators.required, Validators.email],
        nonNullable: true,
      }),
      password: new FormControl('', {
        validators: [Validators.required],
        nonNullable: true,
      }),
      twoFactor: new FormControl('', {
        validators: [],
        nonNullable: true,
      }),
    });
  }

  protected showAccountNotAuthorized() {
    this.toastService.addToast({
      title: this.translocoService.translate(
        'pages.auth.toasts.account_not_found.title',
      ),
      message: this.translocoService.translate(
        'pages.auth.toasts.account_not_found.description',
      ),
      icon: 'error',
      duration: 5000,
    });
  }

  protected showTwoFactorFailed() {
    this.toastService.addToast({
      title: this.translocoService.translate(
        'pages.auth.toasts.two_factor.title',
      ),
      message: this.translocoService.translate(
        'pages.auth.toasts.two_factor.description',
      ),
      icon: 'error',
      duration: 5000,
    });
  }

  public async submit(): Promise<void> {
    if (this.isLoading) {
      return;
    }

    if (!this.form.valid) {
      this.form.markAllAsTouched();
      return;
    }

    if (this.isWaitingForTwoFactor) {
      this.handleTwoFactor();
      return;
    }

    this.handleLogin();
  }

  protected async handleTwoFactor(): Promise<void> {
    try {
      await lastValueFrom(
        this.twoFactorService.verify(this.form.controls.twoFactor.value),
      );
    } catch (e) {
      this.showTwoFactorFailed();
      this.resetForm();

      return;
    }

    this.finalizeLogin();
  }

  protected async handleLogin(): Promise<void> {
    this.isLoading = true;

    await lastValueFrom(this.loginService.csrf());

    try {
      const loginResponse = await lastValueFrom(
        this.loginService.login(
          this.form.controls.email.value,
          this.form.controls.password.value,
        ),
      );

      if (loginResponse.two_factor_required) {
        this.isWaitingForTwoFactor = true;
        this.isLoading = false;

        this.form.controls.email.disable();
        this.form.controls.twoFactor.setValidators([Validators.required]);

        this.changeDetectorRef.detectChanges();

        return;
      }
    } catch (e) {
      this.showAccountNotAuthorized();
      this.resetForm();

      return;
    }

    this.finalizeLogin();
  }

  protected async finalizeLogin(): Promise<void> {
    const response = await lastValueFrom(this.loginService.me());
    this.authService.setUser(response.data, response.metadata);

    await this.router.navigateByUrl('/dashboard');
  }

  protected resetForm(): void {
    this.isLoading = false;
    this.isWaitingForTwoFactor = false;

    this.initForm();
    this.changeDetectorRef.detectChanges();
  }
}
