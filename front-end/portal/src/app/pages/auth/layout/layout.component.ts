import { ChangeDetectionStrategy, Component, inject } from '@angular/core';
import { RouterOutlet } from '@angular/router';
import { ConfigService } from '@services/config.service';

@Component({
  selector: 'pages-auth-layout',
  templateUrl: './layout.component.html',
  standalone: true,
  imports: [RouterOutlet],
  changeDetection: ChangeDetectionStrategy.OnPush,
})
export class LayoutComponent {
  // Injects
  protected configService = inject(ConfigService);
}
