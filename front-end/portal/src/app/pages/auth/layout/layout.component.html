<div class="flex min-h-full">
  <div class="flex flex-1 flex-col justify-center px-4 py-12 sm:px-6 lg:flex-none lg:px-20 xl:px-24 relative bg-no-repeat bg-left" style="background-image: url('images/auth/pattern.svg')">
    <div class="mx-auto w-full max-w-sm lg:w-96 z-20">
      <div>
        @if (configService.config?.environment?.brandStyle === 'lkq') {
          <img class="h-10 w-auto" src="images/logo.svg" alt="Logo">
        } @else {
          <img class="h-10 w-auto" src="images/toads.png" alt="Logo">
        }
      </div>

      <div class="mt-10 space-y-3">
        <router-outlet></router-outlet>
      </div>
    </div>
  </div>
  <div class="relative hidden w-0 flex-1 lg:block">
    @if (configService.config?.environment?.brandStyle === 'lkq') {
      <img class="absolute inset-0 h-full w-full object-cover object-left" src="images/auth/background.png" alt="">
    }
  </div>
</div>
