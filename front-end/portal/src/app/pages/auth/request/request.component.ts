import {
  ChangeDetectionStrategy,
  Component,
  inject,
  OnInit,
} from '@angular/core';
import { LoginService } from '@api/services/authentication/login.service';
import { lastValueFrom } from 'rxjs';
import { ActivatedRoute, Router, RouterLink } from '@angular/router';
import { LoaderComponent } from '@components/loader/loader.component';
import { TranslocoDirective, TranslocoService } from '@jsverse/transloco';
import {
  FormControl,
  FormGroup,
  FormsModule,
  ReactiveFormsModule,
  Validators,
} from '@angular/forms';
import { ToastService } from '@components/toast/toast.service';

interface QueryParams {
  email?: string;
}

interface Form {
  email: FormControl<string>;
}

@Component({
  selector: 'pages-auth-request',
  templateUrl: './request.component.html',
  standalone: true,
  imports: [
    LoaderComponent,
    ReactiveFormsModule,
    TranslocoDirective,
    FormsModule,
    RouterLink,
  ],
  changeDetection: ChangeDetectionStrategy.OnPush,
})
export class RequestComponent implements OnInit {
  // Injects
  private loginService = inject(LoginService);
  private router = inject(Router);
  private toastService = inject(ToastService);
  private translocoService = inject(TranslocoService);
  private route = inject(ActivatedRoute);

  // State
  public isLoading = false;

  // Form
  public form = new FormGroup<Form>({
    email: new FormControl('', {
      validators: [Validators.required, Validators.email],
      nonNullable: true,
    }),
  });

  public ngOnInit(): void {
    this.readRouteParameters();
  }

  protected readRouteParameters(): void {
    this.route.queryParams.subscribe((params: QueryParams) => {
      if (!params?.email) {
        return;
      }

      this.form.controls.email.setValue(params.email);
    });
  }

  protected showRequestSubmitted() {
    this.toastService.addToast({
      title: this.translocoService.translate(
        'pages.auth.toasts.request_submitted.title',
      ),
      message: this.translocoService.translate(
        'pages.auth.toasts.request_submitted.description',
      ),
      icon: 'success',
      duration: 5000,
    });
  }

  public async submit(): Promise<void> {
    if (this.isLoading) {
      return;
    }

    if (!this.form.valid) {
      this.form.markAllAsTouched();
      return;
    }

    this.isLoading = true;

    await lastValueFrom(this.loginService.csrf());

    await lastValueFrom(
      this.loginService.requestPassword(this.form.controls.email.value),
    );

    this.showRequestSubmitted();

    await this.router.navigateByUrl('/auth/login');
  }
}
