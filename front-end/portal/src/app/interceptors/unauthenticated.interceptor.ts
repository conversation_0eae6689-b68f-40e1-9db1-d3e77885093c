import { inject } from '@angular/core';
import {
  HttpRequest,
  HttpErrorResponse,
  HttpInterceptorFn,
  HttpHandlerFn,
} from '@angular/common/http';
import { catchError, first, switchMap, throwError } from 'rxjs';
import { ConfigService } from '@services/config.service';
import { Router } from '@angular/router';
import { ModalService } from '@components/modal/modal.service';

const ignoredEndpoints: string[] = ['auth/logout'];

export const unauthenticatedInterceptor: HttpInterceptorFn = (
  request: HttpRequest<unknown>,
  next: HttpHandlerFn,
) => {
  const configService = inject(ConfigService);
  const modalService = inject(ModalService);
  const router = inject(Router);

  return configService.config$.pipe(
    first((value) => value !== null),
    switchMap(() => {
      if (ignoredEndpoints.some((endpoint) => request.url.endsWith(endpoint))) {
        return next(request);
      }

      return next(request).pipe(
        catchError((error) => {
          if (error instanceof HttpErrorResponse && error.status === 401) {
            modalService.close();
            router.navigateByUrl('/auth/login');
          }

          return throwError(() => error);
        }),
      );
    }),
  );
};
