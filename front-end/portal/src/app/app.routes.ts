import { Routes } from '@angular/router';
import { ContainerComponent } from '@layout/container/container.component';
import { authGuard } from '@guards/auth.guard';
import { SettingsComponent } from './pages/settings/settings.component';
import { adminGuard } from '@guards/admin.guard';
import { LayoutComponent as AuthLayoutComponent } from './pages/auth/layout/layout.component';
import { superAdminGuard } from '@guards/super-admin.guard';

export const routes: Routes = [
  {
    path: 'auth',
    component: AuthLayoutComponent,
    children: [
      {
        path: 'login',
        title: 'Login',
        loadComponent: () =>
          import('./pages/auth/login/login.component').then(
            (m) => m.LoginComponent,
          ),
      },
      {
        path: 'request',
        title: 'Request Password',
        loadComponent: () =>
          import('./pages/auth/request/request.component').then(
            (m) => m.RequestComponent,
          ),
      },
      {
        path: 'reset',
        title: 'Reset Password',
        loadComponent: () =>
          import('./pages/auth/reset/reset.component').then(
            (m) => m.ResetComponent,
          ),
      },
    ],
  },
  {
    path: '',
    redirectTo: 'dashboard',
    pathMatch: 'full',
  },
  {
    path: '',
    component: ContainerComponent,
    canActivate: [authGuard],
    children: [
      {
        path: 'dashboard',
        title: 'Dashboard',
        loadComponent: () =>
          import('./pages/dashboard/dashboard.component').then(
            (m) => m.DashboardComponent,
          ),
      },
      {
        path: 'settings',
        title: 'Settings',
        component: SettingsComponent,
        canActivate: [adminGuard],
        children: [
          {
            path: '',
            redirectTo: 'users',
            pathMatch: 'full',
          },
          {
            path: 'users',
            title: 'Settings - Users',
            loadComponent: () =>
              import('./pages/settings/users/users.component').then(
                (m) => m.UsersComponent,
              ),
          },
          {
            path: 'users/:id',
            title: 'Settings - Users',
            loadComponent: () =>
              import(
                './pages/settings/users/detail/user-detail.component'
              ).then((m) => m.UserDetailComponent),
          },
          {
            path: 'helpdesk',
            title: 'Settings - Helpdesk',
            loadComponent: () =>
              import('./pages/settings/helpdesk/helpdesk.component').then(
                (m) => m.HelpdeskComponent,
              ),
          },
          {
            path: 'connections/google',
            title: 'Settings - Connections - Google',
            loadComponent: () =>
              import(
                './pages/settings/connections/google/google.component'
              ).then((m) => m.GoogleComponent),
          },
          {
            path: 'connections/google-business-profiles',
            title: 'Settings - Connections - Google Business Profiles',
            loadComponent: () =>
              import(
                './pages/settings/connections/google-business-profiles/google-business-profiles.component'
              ).then((m) => m.GoogleBusinessProfilesComponent),
          },
          {
            path: 'connections/google/:id',
            title: 'Settings - Connections - Google',
            loadComponent: () =>
              import(
                './pages/settings/connections/google/detail/google-connection-detail.component'
              ).then((m) => m.GoogleConnectionDetailComponent),
          },
          {
            path: 'connections/youtube',
            title: 'Settings - Connections - Youtube',
            loadComponent: () =>
              import(
                './pages/settings/connections/youtube/youtube.component'
              ).then((m) => m.YoutubeComponent),
          },
          {
            path: 'connections/youtube/:id',
            title: 'Settings - Connections - Youtube',
            loadComponent: () =>
              import(
                './pages/settings/connections/youtube/detail/youtube-connection-detail.component'
              ).then((m) => m.YoutubeConnectionDetailComponent),
          },
          {
            path: 'connections/facebook',
            title: 'Settings - Connections - Facebook',
            loadComponent: () =>
              import(
                './pages/settings/connections/facebook/facebook.component'
              ).then((m) => m.FacebookComponent),
          },
          {
            path: 'connections/facebook/:id',
            title: 'Settings - Connections - Facebook',
            loadComponent: () =>
              import(
                './pages/settings/connections/facebook/detail/facebook-connection-detail.component'
              ).then((m) => m.FacebookConnectionDetailComponent),
          },
          {
            path: 'connections/mailchimp',
            title: 'Settings - Connections - Mailchimp',
            loadComponent: () =>
              import(
                './pages/settings/connections/mailchimp/mailchimp.component'
              ).then((m) => m.MailchimpComponent),
          },
          {
            path: 'connections/mailchimp/add',
            title: 'Settings - Connections - Mailchimp',
            loadComponent: () =>
              import(
                './pages/settings/connections/mailchimp/add/add.component'
              ).then((m) => m.AddComponent),
          },
          {
            path: 'connections/linkedin-ads',
            title: 'Settings - Connections - LinkedIn Ads',
            loadComponent: () =>
              import(
                './pages/settings/connections/linkedin-ads/linkedin-ads.component'
              ).then((m) => m.LinkedinAdsComponent),
          },
          {
            path: 'connections/linkedin-ads/:id',
            title: 'Settings - Connections - LinkedIn Ads',
            loadComponent: () =>
              import(
                './pages/settings/connections/linkedin-ads/detail/linked-in-ads-connection-detail.component'
              ).then((m) => m.LinkedInAdsConnectionDetailComponent),
          },
          {
            path: 'connections/linkedin-community',
            title: 'Settings - Connections - LinkedIn Community',
            loadComponent: () =>
              import(
                './pages/settings/connections/linkedin-community/linkedin-community.component'
              ).then((m) => m.LinkedinCommunityComponent),
          },
          {
            path: 'connections/linkedin-community/:id',
            title: 'Settings - Connections - LinkedIn Community',
            loadComponent: () =>
              import(
                './pages/settings/connections/linkedin-community/detail/linked-in-community-connection-detail.component'
              ).then((m) => m.LinkedInCommunityConnectionDetailComponent),
          },
          {
            path: 'connections/tiktok-organic',
            title: 'Settings - Connections - TikTok Organic',
            loadComponent: () =>
              import(
                './pages/settings/connections/tiktok-organic/tiktok-organic.component'
              ).then((m) => m.TiktokOrganicComponent),
          },
          {
            path: 'connections/tiktok-business',
            title: 'Settings - Connections - TikTok Business',
            loadComponent: () =>
              import(
                './pages/settings/connections/tiktok-business/tiktok-business.component'
              ).then((m) => m.TiktokBusinessComponent),
          },
          {
            path: 'data-source',
            canActivate: [superAdminGuard],
            loadChildren: () =>
              import('./pages/settings/data-source/data-source.routes').then(
                (r) => r.DataSourceRoutes,
              ),
          },
        ],
      },
    ],
  },
  {
    path: 'connect',
    title: 'Connect',
    loadComponent: () =>
      import('./pages/connect/connect.component').then(
        (m) => m.ConnectComponent,
      ),
  },
  {
    path: '**',
    title: '404',
    loadComponent: () =>
      import('./pages/not-found/not-found.component').then(
        (m) => m.NotFoundComponent,
      ),
  },
];
