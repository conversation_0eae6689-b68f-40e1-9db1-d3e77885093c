import {
  ApplicationRef,
  ComponentRef,
  createComponent,
  EnvironmentInjector,
  inject,
  Injectable,
  Type,
} from '@angular/core';
import { ModalComponent } from './modal.component';

@Injectable({
  providedIn: 'root',
})
export class ModalService {
  // State
  public modalComponent?: ComponentRef<ModalComponent>;
  public contentComponent?: ComponentRef<unknown>;

  // Injects
  private appRef = inject(ApplicationRef);
  private injector = inject(EnvironmentInjector);

  public open<C>(
    component: Type<C>,
    componentProperties?: Partial<C>,
  ): ComponentRef<C> {
    if (this.modalComponent) {
      this.close();
    }

    // Create instance of the content component
    this.contentComponent = createComponent<C>(component, {
      environmentInjector: this.injector,
    });

    // Map properties
    if (componentProperties) {
      Object.entries(componentProperties).forEach(([key, value]) => {
        this.contentComponent?.setInput(key, value);
      });
    }

    // Create instance of the modal component and project the instance of the content component
    this.modalComponent = createComponent(ModalComponent, {
      environmentInjector: this.injector,
      projectableNodes: [[this.contentComponent.location.nativeElement]],
    });

    document.body.appendChild(this.modalComponent.location.nativeElement);

    // Attach views to the changeDetector
    this.appRef.attachView(this.contentComponent.hostView);
    this.appRef.attachView(this.modalComponent.hostView);

    return this.contentComponent as ComponentRef<C>;
  }

  public close() {
    if (this.modalComponent) {
      this.appRef.detachView(this.modalComponent.hostView);

      this.modalComponent.destroy();
      this.modalComponent = undefined;
    }

    if (this.contentComponent) {
      this.appRef.detachView(this.contentComponent.hostView);

      this.contentComponent.destroy();
      this.contentComponent = undefined;
    }
  }
}
