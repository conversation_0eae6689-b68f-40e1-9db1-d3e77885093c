import { Component, HostListener, inject } from '@angular/core';
import { ModalService } from './modal.service';
import { CommonModule } from '@angular/common';

@Component({
  selector: 'app-modal',
  templateUrl: './modal.component.html',
  standalone: true,
  imports: [CommonModule],
})
export class ModalComponent {
  // Injects
  private modalService = inject(ModalService);

  @HostListener('document:keydown.escape')
  onEscape() {
    this.modalService.close();
  }

  public onClose() {
    this.modalService.close();
  }
}
