import {
  ChangeDetectionStrategy,
  Component,
  inject,
  Input,
} from '@angular/core';
import { CommonModule } from '@angular/common';
import { TranslocoDirective } from '@jsverse/transloco';
import { Subject } from 'rxjs';
import { ModalService } from '@components/modal/modal.service';

export enum ConfirmationModalType {
  WARNING = 'warning',
}

@Component({
  selector: 'confirmation-modal',
  templateUrl: './confirmation-modal.component.html',
  standalone: true,
  imports: [CommonModule, TranslocoDirective],
  changeDetection: ChangeDetectionStrategy.OnPush,
})
export class ConfirmationModalComponent {
  // Inputs
  @Input() public type = ConfirmationModalType.WARNING;
  @Input() public title?: string;
  @Input() public description?: string;
  @Input() public cancelButton?: string;
  @Input() public confirmationButton?: string;

  // State
  public status$ = new Subject<boolean>();

  // Enums
  public confirmationModalType = ConfirmationModalType;

  // Injects
  public modalService = inject(ModalService);

  public confirm(): void {
    this.status$.next(true);
    this.status$.complete();

    this.close();
  }

  public cancel(): void {
    this.status$.next(false);
    this.status$.complete();

    this.close();
  }

  public close(): void {
    this.modalService.close();
  }
}
