import { inject, Injectable } from '@angular/core';
import { ModalService } from '@components/modal/modal.service';
import { ConfirmationModalComponent } from '@components/confirmation-modal/confirmation-modal.component';
import { lastValueFrom } from 'rxjs';

@Injectable({
  providedIn: 'root',
})
export class ConfirmationModalService {
  // Injects
  private modalService = inject(ModalService);

  public async confirm(
    context: {
      confirmed?: (() => void | Promise<void>) | null;
      cancelled?: (() => void) | null;
    } | null = null,
    translationKeys:
      | {
          title?: string;
          description?: string;
          cancelButton?: string;
          confirmationButton?: string;
        }
      | undefined = undefined,
  ): Promise<void> {
    const componentRef = this.modalService.open(
      ConfirmationModalComponent,
      translationKeys,
    );
    const result = await lastValueFrom(componentRef.instance.status$);

    if (result && context?.confirmed) {
      context.confirmed();
      return;
    }

    if (context?.cancelled) {
      context.cancelled();
    }
  }
}
