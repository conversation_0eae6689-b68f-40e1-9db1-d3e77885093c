import { Injectable } from '@angular/core';
import { Subject } from 'rxjs';

export interface Toast {
  identifier?: number;
  duration?: number;
  title?: string;
  icon?: 'success' | 'error' | 'warning';
  message: string;
}

@Injectable({
  providedIn: 'root',
})
export class ToastService {
  // State
  private toasts: Toast[] = [];
  public toasts$ = new Subject<Toast[]>();

  public addToast(toast: Toast) {
    if (!toast.identifier) {
      toast.identifier = Math.floor(Math.random() * 1000);
    }

    this.toasts = [toast, ...this.toasts];
    this.toasts$.next(this.toasts);

    if (toast.duration) {
      setTimeout(() => {
        this.removeToast(toast.identifier);
      }, toast.duration);
    }
  }

  public removeToast(identifier: number | undefined) {
    if (!identifier) {
      return;
    }

    this.toasts = this.toasts.filter(
      (toast) => toast.identifier !== identifier,
    );
    this.toasts$.next(this.toasts);
  }
}
