{"general": {"toasts": {"error": {"title": "Whoops! Something went wrong", "description": "Something went wrong. Please try again or contact support."}, "success": {"title": "Action completed successfully.", "description": "The action you just performed has been successfully executed."}}, "select": "Select", "search": "Search.."}, "layout": {"container": {"sign_out": "Log out", "menu": {"dashboard": "Dashboard", "settings": "Settings"}}}, "components": {"confirmation-modal": {"warning": {"title": "Are you certain?", "description": "This action cannot be undone.", "cancel_button": "Cancel", "confirmation_button": "Confirm"}}}, "pages": {"auth": {"toasts": {"account_not_found": {"title": "Account not found", "description": "The email and/or password is incorrect."}, "two_factor": {"title": "Two factor failure", "description": "The provided code does not match."}, "request_submitted": {"title": "Request submitted", "description": "Check your email for a reset link."}, "reset_succeeded": {"title": "Reset successful", "description": "Your password has been changed."}}, "login": {"submit": "Sign In", "forgot_password": "Forgot password?", "form": {"email": {"label": "E-mail", "placeholder": "<EMAIL>"}, "password": {"label": "Password", "placeholder": "************"}, "2fa": {"label": "Two factor", "placeholder": "123abc"}}}, "request": {"title": "Need access to your account?", "sub_title": "Enter your email below to get started.", "back_to_login": "Back", "submit": "Request", "form": {"email": {"label": "E-mail", "placeholder": "<EMAIL>"}}}, "reset": {"title": "Set your password", "sub_title": "Enter your new password to continue.", "back_to_login": "Back", "submit": "Reset", "form": {"email": {"label": "E-mail"}, "password": {"label": "Password", "placeholder": "************"}}}}, "dashboard": {"title": "Dashboard", "no_dashboards": "Your account doesn't have any dashboards configured. Please request your administrator to assign dasbhoards.", "no_data": "No data", "filters": {"date": "Date", "channel": "Channel", "business_unit": "Business unit", "region": "Region", "placeholder": "No filters", "value": "{count, plural, =0 {} one {1 filter} other {# filters}}"}, "date_ranges": {"this_week": "This Week", "last_week": "Last Week", "this_month": "This Month", "last_month": "Last Month", "this_quarter": "This Quarter", "last_quarter": "Last Quarter", "this_year": "This Year", "last_year": "Last Year"}, "modals": {"view": {"index": {"title": "Views", "description": "Use views to load pre-defined dashboard filters.", "table": {"select": "Select", "delete": "Delete", "no_results": "No views found"}, "delete": {"title": "Delete view?", "description": "Are you certain to delete this view? This action cannot be undone."}}, "add": {"title": "Add view", "description": "Save current filters as a reusable view.", "form": {"name": {"label": "Name", "placeholder": "Marketing view"}}, "buttons": {"save": "Save", "cancel": "Cancel"}}}, "target": {"title": "Update target", "description": "Set the target for the {{title}} widget.", "form": {"target": {"label": "Target"}}, "buttons": {"save": "Save", "cancel": "Cancel", "delete": "Remove target"}}, "dashboard": {"index": {"title": "Dashboards", "description": "Switch dashboards to view other data sources.", "table": {"select": "Select", "selected": "Selected", "no_results": "No dashboards found"}}, "detail": {"title": "Detail: {name}"}}}}, "settings": {"title": "Settings", "menu": {"users": "Users", "connections": "Connections", "google": "Google", "youtube": "Youtube", "facebook": "Facebook", "mailchimp": "Mailchimp", "linkedin-ads": "LinkedIn Ads", "linkedin-community": "LinkedIn Community", "tiktok-organic": "TikTok Organic", "tiktok-business": "TikTok Business", "helpdesk": "Helpdesk", "google-business-profiles": "Google Business Profiles", "data-source": "Data Source", "business-units": "Business Units", "regions": "Regions", "sources": "Sources", "dashboards": "Dashboards", "onboarding": "Onboarding"}, "users": {"detail": {"add": {"title": "Add user", "description": "Add a new user which can login to the dashboard."}, "edit": {"title": "Edit user {name}", "description": "Adjust the details of this user."}, "save": "Save", "cancel": "Cancel", "form": {"name": {"label": "Name", "placeholder": "<PERSON>"}, "email": {"label": "E-mail", "placeholder": "<EMAIL>"}, "role": {"label": "Role"}, "dashboards": {"label": "Dashboards"}}, "toggle": {"select": "Select all", "deselect": "Deselect all"}}, "index": {"title": "Users", "add": "Add", "edit": "Edit", "delete": "Delete", "modals": {"delete": {"title": "Delete user?", "description": "Are you certain to delete this user? This action cannot be undone."}}}}, "google": {"index": {"title": "Google Accounts", "share": "Share", "details": "Details", "delete": "Delete", "no_accounts": "No accounts connected", "toasts": {"share": {"title": "Link copied in clipboad", "description": "Shareable link to connect Google Account has been copied!"}, "deleted": {"title": "Deletion request sent", "description": "A request to delete the account has been sent!"}}, "modals": {"delete": {"title": "Delete Google Account?", "description": "Deletion of connected accounts requires helpdesk interaction. Are you sure you want to request deletion of this account?"}}}, "detail": {"title": "Google Account {name} connections", "description": "Resources below are connected to data sources used in dashboards.", "no_connections": "No connected resources used in data sources.", "latest_sync": "Last synced at", "back": "Back to Google Accounts"}}, "google-business-profiles": {"index": {"title": "Google Business Profiles Accounts", "share": "Share", "toasts": {"share": {"title": "Link copied in clipboad", "description": "Shareable link to connect Google Business Profiles Account has been copied!"}}}}, "youtube": {"index": {"title": "Youtube Accounts", "share": "Share", "details": "Details", "delete": "Delete", "no_accounts": "No accounts connected", "toasts": {"share": {"title": "Link copied in clipboad", "description": "Shareable link to connect Youtube Account has been copied!"}, "deleted": {"title": "Deletion request sent", "description": "A request to delete the account has been sent!"}}, "modals": {"delete": {"title": "Delete Youtube Account?", "description": "Deletion of connected accounts requires helpdesk interaction. Are you sure you want to request deletion of this account?"}}}, "detail": {"title": "Youtube Account {name} connections", "description": "Resources below are connected to data sources used in dashboards.", "no_connections": "No connected resources used in data sources.", "latest_sync": "Last synced at", "back": "Back to Youtube Accounts"}}, "facebook": {"index": {"title": "Facebook Accounts", "share": "Share", "details": "Details", "delete": "Delete", "no_accounts": "No accounts connected", "toasts": {"share": {"title": "Link copied in clipboad", "description": "Shareable link to connect Facebook Account has been copied!"}, "deleted": {"title": "Deletion request sent", "description": "A request to delete the account has been sent!"}}, "modals": {"delete": {"title": "Delete Facebook Account?", "description": "Deletion of connected accounts requires helpdesk interaction. Are you sure you want to request deletion of this account?"}}}, "detail": {"title": "Facebook Account {name} connections", "description": "Resources below are connected to data sources used in dashboards.", "no_connections": "No connected resources used in data sources.", "latest_sync": "Last synced at", "back": "Back to Facebook Accounts"}}, "mailchimp": {"add": {"title": "Add Mailchimp api key", "description": "Add a Mailchimp api key", "save": "Save", "cancel": "Cancel", "form": {"name": {"label": "Name", "placeholder": "LKQ BE"}, "api-key": {"label": "Api key", "placeholder": "7*************************************"}}}, "index": {"title": "Mailchimp Accounts", "delete": "Delete", "no_accounts": "No accounts connected", "add": "Add", "toasts": {"deleted": {"title": "Deletion request sent", "description": "A request to delete the account has been sent!"}}, "modals": {"delete": {"title": "Delete Mailchimp Account?", "description": "Deletion of connected accounts requires helpdesk interaction. Are you sure you want to request deletion of this account?"}}}}, "linkedin-ads": {"index": {"title": "LinkedIn Accounts for Ads", "share": "Share", "details": "Details", "delete": "Delete", "no_accounts": "No accounts connected", "toasts": {"share": {"title": "Link copied in clipboad", "description": "Shareable link to connect LinkedIn Account has been copied!"}, "deleted": {"title": "Deletion request sent", "description": "A request to delete the account has been sent!"}}, "modals": {"delete": {"title": "Delete LinkedIn Account?", "description": "Deletion of connected accounts requires helpdesk interaction. Are you sure you want to request deletion of this account?"}}}, "detail": {"title": "LinkedIn Accounts {name} connections", "description": "Resources below are connected to data sources used in dashboards.", "no_connections": "No connected resources used in data sources.", "latest_sync": "Last synced at", "back": "Back to LinkedIn Accounts for Ads"}}, "linkedin-community": {"index": {"title": "LinkedIn Accounts for Community", "share": "Share", "details": "Details", "delete": "Delete", "no_accounts": "No accounts connected", "toasts": {"share": {"title": "Link copied in clipboad", "description": "Shareable link to connect LinkedIn Account has been copied!"}, "deleted": {"title": "Deletion request sent", "description": "A request to delete the account has been sent!"}}, "modals": {"delete": {"title": "Delete LinkedIn Account?", "description": "Deletion of connected accounts requires helpdesk interaction. Are you sure you want to request deletion of this account?"}}}, "detail": {"title": "LinkedIn Accounts {name} connections", "description": "Resources below are connected to data sources used in dashboards.", "no_connections": "No connected resources used in data sources.", "latest_sync": "Last synced at", "back": "Back to LinkedIn Accounts for Community"}}, "tiktok-organic": {"index": {"title": "TikTok Organic Accounts", "share": "Share", "details": "Details", "delete": "Delete", "no_accounts": "No accounts connected", "toasts": {"share": {"title": "Link copied in clipboad", "description": "Shareable link to connect TikTok Account has been copied!"}, "deleted": {"title": "Deletion request sent", "description": "A request to delete the account has been sent!"}}, "modals": {"delete": {"title": "Delete TikTok Account?", "description": "Deletion of connected accounts requires helpdesk interaction. Are you sure you want to request deletion of this account?"}}}}, "tiktok-business": {"index": {"title": "TikTok Business Accounts", "share": "Share", "details": "Details", "delete": "Delete", "no_accounts": "No accounts connected", "toasts": {"share": {"title": "Link copied in clipboad", "description": "Shareable link to connect TikTok Business Account has been copied!"}, "deleted": {"title": "Deletion request sent", "description": "A request to delete the account has been sent!"}}, "modals": {"delete": {"title": "Delete TikTok Business Account?", "description": "Deletion of connected accounts requires helpdesk interaction. Are you sure you want to request deletion of this account?"}}}}, "helpdesk": {"request": {"title": "Helpdesk", "description": "Contact the helpdesk for any problems or requests.", "send": "Submit request", "form": {"subject": "Subject", "message": "Message"}, "toasts": {"submitted": {"title": "Request sent!", "description": "Your message has been successfully submitted."}}}}, "data-source": {"business-units": {"index": {"title": "Business Units", "add": "Add", "edit": "Edit"}, "detail": {"add": {"title": "Add Business Unit", "description": "Add a new Business Unit."}, "edit": {"title": "Edit Business Unit {name}", "description": "Adjust the details of this Business Unit."}, "save": "Save", "cancel": "Cancel", "form": {"name": {"label": "Name", "placeholder": "LKQ"}}}}, "region": {"index": {"title": "Regions", "add": "Add", "edit": "Edit"}, "detail": {"add": {"title": "Add Region", "description": "Add a new Region."}, "edit": {"title": "Edit Region {name}", "description": "Adjust the details of this Region."}, "save": "Save", "cancel": "Cancel", "form": {"name": {"label": "Name", "placeholder": "NL"}}}}, "dashboards": {"index": {"title": "Dashboards", "add": "Add", "edit": "Edit"}, "detail": {"add": {"title": "Add dashboard", "description": "Add a new dashboard."}, "edit": {"title": "Edit Dashboard {name}", "description": "Adjust the details of this dashboard."}, "save": "Save", "cancel": "Cancel", "form": {"title": {"label": "Title", "placeholder": "LKQ"}}}}, "sources": {"index": {"title": "Sources", "add": "Add", "details": "Details", "delete": "Delete"}, "detail": {"cancel": "Cancel", "save": "Save", "buttons": {"add": "Add", "remove": "Remove"}, "add": {"title": "Add Data Source", "description": "Add a new data source."}, "edit": {"title": "Edit Data Source", "description": "Adjust the details of this data source."}, "form": {"type": {"label": "Data Source type"}, "title": {"label": "Title", "placeholder": "ATF Google Analytics Property"}, "source": {"label": "Source"}, "sources": {"label": "Sources"}, "region": {"label": "Region"}, "business-unit": {"label": "Business Unit"}, "dashboards": {"label": "Dashboards"}}, "toggle": {"select": "Select all", "deselect": "Deselect all"}}}, "onboarding": {"title": "Onboard new business unit", "description": "Configure a new business unit and select which platforms to integrate.", "business-unit": {"title": "Business unit information", "form": {"name": {"label": "Business unit name", "placeholder": "eg., LKQ UK"}}}, "dashboard": {"title": "Location & Dashboard settings", "form": {"region": {"label": "Region", "placeholder": "Select a region", "create": "Or create a new one"}, "dashboard": {"label": "Dashboard name", "placeholder": "eg., LKQ UK"}}}, "extra-options": {"title": "Extra options", "form": {"items": {"label": "Multiple items per platform", "description": "Whether this business unit has one or more items to connect. So for example it can have one or multiple facebook pages."}, "targets": {"label": "Targets", "placeholder": "Describe your targets and goals for this business unit"}}}, "platforms": {"title": "Platform selection", "description": "Which platforms do you want to load in this business unit."}}}}, "connect": {"state": {"prompt": {"title": "Connect your account", "description": "LKQ Fource requires access to your account. Click the button below to connect this."}, "success": {"title": "Connection succeeded!", "description": "You can now close this screen."}, "missing_scopes": {"title": "Connection failed!", "description": "Not all required rights have been connected. Please try again below."}}}, "not_found": {"title": "Page not found", "description": "Sorry, the page you are looking for cannot be found.", "back": "Back to dashboard"}}, "enums": {"dashboard": {"widget-detail": {"datasource": "<PERSON><PERSON>", "business_unit": "Business unit", "region": "Regio"}, "post-detail": {"TOTAL": "Total", "SINGLE": "Single"}}, "authentication": {"role": {"ADMIN": "Administrator", "SUPER_ADMIN": "Super administrator", "MASTER_ADMIN": "Super administrator", "USER": "Standard", "VIEW_MANAGER": "View manager"}}, "data-source-type": {"GOOGLE_ADS": "Google Ads (Ad account)", "GOOGLE_ANALYTICS": "Google Analytics (Property)", "GOOGLE_BUSINESS_PROFILES": "Google Business Profiles", "YOUTUBE": "Youtube (Channel)", "LINKEDIN_ADS": "Linkedin Ads (Ad account)", "LINKEDIN_ORGANISATIONS": "Linkedin Organisations (Organisation)", "MAILCHIMP": "<PERSON><PERSON><PERSON> (Audience)", "FACEBOOK_ADS": "Facebook Ads (Ad account)", "FACEBOOK_PAGES": "Facebook Pages (Page)", "INSTAGRAM": "Instagram (Account)", "TIKTOK": "<PERSON><PERSON><PERSON><PERSON> (Account)"}}, "pagination": {"previous": "Previous", "next": "Next", "shows": "Showing", "until": "till", "of": "from the", "results": "results"}, "filters": {"date": "Date", "channel": "Channel", "business_unit": "Business unit", "region": "Region", "placeholder": "No filters", "dashboards": "Dashboards", "value": "{count, plural, =0 {} one {1 filter} other {# filters}}"}}