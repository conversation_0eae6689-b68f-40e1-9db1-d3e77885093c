{"name": "front-end", "version": "0.0.0", "scripts": {"ng": "ng", "start": "ng serve", "build": "ng build", "watch": "ng build --watch --configuration development", "test": "ng test", "lint": "npx prettier --check \"src/**/*.ts\"", "lint:fix": "npx prettier --write \"src/**/*.ts\""}, "private": true, "dependencies": {"@angular/animations": "^18.1.0", "@angular/common": "^18.1.0", "@angular/compiler": "^18.1.0", "@angular/core": "^18.1.0", "@angular/forms": "^18.1.0", "@angular/platform-browser": "^18.1.0", "@angular/platform-browser-dynamic": "^18.1.0", "@angular/router": "^18.1.0", "@jsverse/transloco": "^7.4.3", "@jsverse/transloco-messageformat": "^7.0.1", "apexcharts": "^3.52.0", "date-fns": "^3.6.0", "ng-apexcharts": "^1.11.0", "ng-zorro-antd": "^18.1.1", "ngx-mask": "^19.0.6", "rxjs": "~7.8.0", "tslib": "^2.3.0", "zone.js": "~0.14.3"}, "devDependencies": {"@angular-devkit/build-angular": "^18.1.3", "@angular/cli": "^18.1.3", "@angular/compiler-cli": "^18.1.0", "@tailwindcss/aspect-ratio": "^0.4.2", "@tailwindcss/forms": "^0.5.7", "@tailwindcss/typography": "^0.5.13", "prettier": "^3.3.3", "prettier-eslint": "^16.3.0", "prettier-plugin-tailwindcss": "^0.6.5", "tailwindcss": "^3.4.7", "typescript": "~5.5.2", "winston": "^3.13.1"}}