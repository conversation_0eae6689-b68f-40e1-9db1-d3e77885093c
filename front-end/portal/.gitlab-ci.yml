cache:
  key: $CI_COMMIT_REF_NAME
  paths:
    - vendor/

front-end:portal:build:
  stage: build
  when: on_success
  image: registry.gitlab.com/localium/tooling/docker-containers/php8.3:v0.0.3
  extends:
    - .front-end:portal:working-directory
  script:
    # Vendor files
    - npm ci
    # Prettier
    - npm run lint
    # Build
    - npm run build
  artifacts:
    expire_in: 1 days
    paths:
      - ./front-end/portal/dist

front-end:portal:deploy:develop:
  variables: {
    CONFIG: "$DEVELOP_CONFIG",
    USER: runcloud,
    HOST: ************,
    PORT: 22,
    SSH: "ssh -tt $USER@$HOST -p $PORT -o StrictHostKeyChecking=no -q",
    CURRENT_PATH: "/home/<USER>/webapps/fource-front-end/current",
    RELEASE_PATH: "/home/<USER>/webapps/fource-front-end/releases/$CI_PIPELINE_ID",
  }
  stage: deploy
  image: registry.gitlab.com/localium/tooling/docker-containers/php8.3:v0.0.3
  only:
    - /^release\//
  when: manual
  environment:
    name: develop
    url: $DEVELOP_URL
  extends: .front-end:portal:deployment
  script:
    - echo 'deployment successful!'

front-end:portal:deploy:toads:
  variables: {
    CONFIG: "$TOADS_CONFIG",
    USER: runcloud,
    HOST: ***********,
    PORT: 22,
    SSH: "ssh -tt $USER@$HOST -p $PORT -o StrictHostKeyChecking=no -q",
    CURRENT_PATH: "/home/<USER>/webapps/toads-front-end/current",
    RELEASE_PATH: "/home/<USER>/webapps/toads-front-end/releases/$CI_PIPELINE_ID",
  }
  stage: deploy
  image: registry.gitlab.com/localium/tooling/docker-containers/php8.3:v0.0.3
  only:
    - /^release\//
  when: manual
  environment:
    name: develop
    url: $DEVELOP_URL
  extends: .front-end:portal:deployment
  script:
    # Replace favicon
    - $SSH "mv $RELEASE_PATH/images/favicon-toads.ico $RELEASE_PATH/favicon.ico"
    - echo 'deployment successful!'

front-end:portal:deploy:main:
  variables: {
    CONFIG: "$MAIN_V2_CONFIG",
    USER: runcloud,
    HOST: ***********,
    PORT: 22,
    SSH: "ssh -tt $USER@$HOST -p $PORT -o StrictHostKeyChecking=no -q",
    CURRENT_PATH: "/home/<USER>/webapps/fource-front-end/current",
    RELEASE_PATH: "/home/<USER>/webapps/fource-front-end/releases/$CI_PIPELINE_ID",
  }
  stage: deploy
  image: registry.gitlab.com/localium/tooling/docker-containers/php8.3:v0.0.3
  only:
    - tags
  when: manual
  environment:
    name: main
    url: https://marketingdashboard.lkqeurope.nl
  extends: .front-end:portal:deployment
  script:
    - echo 'deployment successful!'


.front-end:portal:deployment:
  before_script:
    # Add SSH
    - mkdir -p ~/.ssh
    - eval $(ssh-agent -s)
    - echo "$SSH_KEY" | tr -d '\r' | ssh-add - > /dev/null
    # Create release directory
    - $SSH "mkdir -p $RELEASE_PATH"
    # Set config
    - rm -rf front-end/portal/dist/front-end/config.json
    - echo "$CONFIG" > front-end/portal/dist/front-end/config.json
    # Compress files
    - cd ./front-end/portal/dist/front-end
    - tar -czf artifacts.tar.gz ./*
    # Upload files
    - scp -P$PORT -r artifacts.tar.gz $USER@$HOST:$RELEASE_PATH
    # Uncompress files
    - $SSH "tar -xzf $RELEASE_PATH/artifacts.tar.gz -C $RELEASE_PATH"
    # Delete archive
    - $SSH "rm -rf $RELEASE_PATH/artifacts.tar.gz"
    # Activate release
    - $SSH "ln -nfs $RELEASE_PATH $CURRENT_PATH";

.front-end:portal:working-directory:
  before_script:
    - cd ./front-end/portal
