/** @type {import('tailwindcss').Config} */
const defaultTheme = require('tailwindcss/defaultTheme');

module.exports = {
  content: ['./src/**/*.{html,ts}'],
  plugins: [
    require('@tailwindcss/forms'),
    require('@tailwindcss/typography'),
    require('@tailwindcss/aspect-ratio'),
  ],
  theme: {
    extend: {
      width: {
        100: '25rem',
      },
      fontFamily: {
        sharp: ['SharpGrotesk', 'sans-serif'],
        sans: ['SharpGrotesk', ...defaultTheme.fontFamily.sans],
      },
      colors: {
        lkq: {
          'deep-black': '#000',
          'pure-white': '#fff',
          'electric-blue': '#085CF0',
          'citrus-yellow': '#F3FF66',
          gray: {
            100: '#FAFAFA',
            200: '#F2F2F2',
            500: '#D2D2D2',
            800: '#232323',
            900: '#1A1A1A',
          },
          blue: {
            100: '#C0DDFF',
            200: '#5F9AFF',
            500: '#0043B8',
            800: '#002C7B',
            900: '#040E32',
          },
          sustainability: {
            100: '#B3FFC4',
            300: '#7EED7B',
            spring: '#17E581',
            800: '#04753F',
            900: '#012A0A',
          },
          infographic: {
            teal: {
              100: '#BBECED',
              500: '#5FAFAA',
              700: '#27758E',
            },
            purple: {
              100: '#AFB5F3',
              500: '#6973E0',
            },
          },
        },
      },
    },
  },
};
